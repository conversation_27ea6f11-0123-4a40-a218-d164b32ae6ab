# PayPal SDK 初始化失败 - 故障排除指南

## 问题症状
- 错误信息: "PayPal SDK failed to initialize"
- PayPal按钮不显示或显示错误
- 控制台显示SDK加载相关错误

## 快速诊断步骤

### 1. 检查环境变量
```bash
# 确认环境变量已设置
echo $NEXT_PUBLIC_PAYPAL_CLIENT_ID
```

**预期结果**: 应该显示一个80字符左右的PayPal Client ID

### 2. 检查网络连接
在浏览器中直接访问PayPal SDK URL:
```
https://www.paypal.com/sdk/js?client-id=YOUR_CLIENT_ID&currency=USD&intent=capture&components=buttons
```

**预期结果**: 应该返回JavaScript代码，不是404或网络错误

### 3. 检查浏览器控制台
1. 打开浏览器开发者工具 (F12)
2. 查看Console标签页
3. 查找以下错误类型:
   - 网络错误 (Network Error)
   - CORS错误
   - 脚本加载错误
   - PayPal相关错误

## 常见问题和解决方案

### 问题1: Client ID未设置或无效
**症状**: 
- 环境变量检查显示"Not Set"
- SDK URL返回400错误

**解决方案**:
1. 检查 `.env.local` 文件
2. 确认变量名为 `NEXT_PUBLIC_PAYPAL_CLIENT_ID`
3. 重启开发服务器
4. 验证Client ID格式正确

### 问题2: 网络连接问题
**症状**:
- SDK加载超时
- 网络请求失败

**解决方案**:
1. 检查网络连接
2. 尝试使用VPN或更换网络
3. 检查防火墙设置
4. 确认没有广告拦截器阻止PayPal脚本

### 问题3: CORS或安全策略问题
**症状**:
- CORS错误
- Content Security Policy错误

**解决方案**:
1. 确认在HTTPS环境下测试（生产环境）
2. 检查CSP设置
3. 在开发环境中暂时禁用严格的安全策略

### 问题4: 脚本加载时机问题
**症状**:
- 脚本加载但PayPal对象未定义
- 间歇性失败

**解决方案**:
1. 使用备用PayPal按钮组件
2. 增加重试次数和延迟
3. 检查脚本加载策略

## 使用诊断工具

### 1. PayPal诊断组件
访问结账页面，查看"PayPal 诊断"部分:
- ✅ 绿色表示正常
- ❌ 红色表示有问题

### 2. 备用PayPal按钮
如果主要按钮失败，使用页面下方的"备用PayPal按钮"

### 3. 简化测试组件
查看"简化PayPal测试"的实时日志

## 手动测试步骤

### 测试1: 直接脚本加载
在浏览器控制台中运行:
```javascript
const script = document.createElement('script');
script.src = 'https://www.paypal.com/sdk/js?client-id=YOUR_CLIENT_ID&currency=USD&intent=capture&components=buttons';
script.onload = () => console.log('PayPal SDK loaded:', !!window.paypal);
script.onerror = (e) => console.error('PayPal SDK failed:', e);
document.head.appendChild(script);
```

### 测试2: PayPal对象检查
```javascript
// 检查PayPal对象
console.log('window.paypal:', window.paypal);
console.log('PayPal methods:', window.paypal ? Object.keys(window.paypal) : 'Not available');
console.log('Buttons method:', typeof window.paypal?.Buttons);
```

### 测试3: 创建测试按钮
```javascript
if (window.paypal && window.paypal.Buttons) {
  window.paypal.Buttons({
    createOrder: () => Promise.resolve('test-order'),
    onApprove: () => console.log('Test approved')
  }).render('#test-container');
}
```

## 环境特定解决方案

### 开发环境 (localhost)
1. 确认使用HTTP或HTTPS
2. 检查端口是否被防火墙阻止
3. 尝试不同的浏览器

### 生产环境
1. 必须使用HTTPS
2. 确认域名已在PayPal应用中配置
3. 检查CDN或代理设置

### 沙盒环境
1. 确认使用沙盒Client ID
2. 检查沙盒账户状态
3. 验证沙盒应用配置

## 高级故障排除

### 1. 网络层面
```bash
# 检查DNS解析
nslookup www.paypal.com

# 检查连接
curl -I https://www.paypal.com/sdk/js

# 检查特定SDK URL
curl -I "https://www.paypal.com/sdk/js?client-id=YOUR_CLIENT_ID&currency=USD&intent=capture&components=buttons"
```

### 2. 浏览器层面
1. 清除浏览器缓存
2. 禁用扩展程序
3. 尝试隐身模式
4. 检查浏览器版本兼容性

### 3. 应用层面
1. 检查Next.js版本兼容性
2. 验证TypeScript配置
3. 检查构建输出

## 联系支持

如果以上步骤都无法解决问题:

1. **收集信息**:
   - 浏览器类型和版本
   - 操作系统
   - 网络环境
   - 完整的错误日志
   - 诊断工具的输出

2. **PayPal开发者支持**:
   - 访问 PayPal Developer Community
   - 提交技术支持票据
   - 查看PayPal状态页面

3. **项目支持**:
   - 检查项目GitHub Issues
   - 查看相关文档
   - 联系项目维护者

## 预防措施

1. **监控**:
   - 设置PayPal SDK加载监控
   - 实现错误上报
   - 定期检查PayPal服务状态

2. **备用方案**:
   - 实现多个PayPal按钮组件
   - 提供其他支付方式
   - 优雅的错误处理

3. **测试**:
   - 定期测试不同环境
   - 自动化PayPal集成测试
   - 监控用户反馈

---

**最后更新**: 2024年5月26日
**适用版本**: PayPal JavaScript SDK v5+
