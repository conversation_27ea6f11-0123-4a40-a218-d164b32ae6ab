# PayPal Vault 支付测试指南

## 概述

本项目现在支持两种类型的Vault支付：
1. **Demo Vault支付** - 用于开发和测试，不调用真实PayPal API
2. **真实Vault支付** - 调用真实PayPal API（需要有效的vault token）

## 支付类型识别

系统根据 `paymentMethodId` 的前缀来判断支付类型：
- 以 `demo_` 或 `test_` 开头：使用Demo支付
- 其他格式：尝试真实PayPal API调用

## 测试方法

### 1. Demo Vault支付测试

#### 通过测试页面
1. 访问 `http://localhost:3000/test-vault`
2. 使用默认的支付方式ID：`demo_vault_method_123`
3. 点击"测试 Demo Vault 支付"
4. 查看成功结果

#### 通过结账流程
1. 访问 `http://localhost:3000/vault`
2. 查看已保存的支付方式（包含demo数据）
3. 在结账页面选择已保存的支付方式
4. 完成支付流程

### 2. 真实Vault支付测试

#### 前提条件
- 需要有效的PayPal Vault Token
- PayPal沙盒或生产环境配置正确

#### 测试步骤
1. 访问 `http://localhost:3000/test-vault`
2. 输入真实的vault token作为支付方式ID
3. 点击"测试真实 Vault 支付"
4. 系统将调用真实PayPal API

## API端点

### 获取支付方式
```
GET /api/paypal/payment-methods/{customerId}
```
返回该客户的已保存支付方式列表（包含demo数据）

### Vault支付
```
POST /api/paypal/capture-vault-payment
Content-Type: application/json

{
  "amount": 10.00,
  "currency": "USD",
  "description": "Test Payment",
  "paymentMethodId": "demo_vault_method_123"
}
```

## Demo数据

系统提供以下demo支付方式：

### 支付方式1
- ID: `demo_vault_method_123`
- 类型: Credit Card
- 品牌: VISA
- 尾号: 1234
- 默认: 是

### 支付方式2
- ID: `demo_vault_method_456`
- 类型: Credit Card
- 品牌: MC (MasterCard)
- 尾号: 5678
- 默认: 否

## 数据库记录

### Demo支付
- 订单ID格式: `DEMO_VAULT_{timestamp}`
- 交易ID格式: `DEMO_VAULT_TXN_{timestamp}`
- 支付方式: `PAYPAL_VAULT_DEMO`
- 状态: `COMPLETED`

### 真实支付
- 使用PayPal返回的真实订单ID
- 使用PayPal返回的真实交易ID
- 支付方式: `PAYPAL_VAULT`
- 状态: 根据PayPal API返回

## 前端页面

### 测试页面 (`/test-vault`)
- 直接测试Vault支付API
- 支持Demo和真实支付测试
- 显示完整的响应数据

### 支付方式管理 (`/vault`)
- 查看已保存的支付方式
- 管理默认支付方式
- 删除支付方式

### 结账页面 (`/checkout`)
- 选择新支付方式或已保存支付方式
- 完整的支付流程
- 支持Vault支付

### 成功页面 (`/success`)
- 显示支付结果
- 区分普通支付和Vault支付
- 显示订单详情

## 错误处理

系统会处理以下错误情况：
- 无效的支付方式ID
- 无效的金额
- PayPal API错误
- 网络连接错误

## 日志

查看后端日志以了解支付处理过程：
```bash
# 启动后端时查看日志
cd backend
./mvnw spring-boot:run
```

关键日志信息：
- `Processing demo vault payment` - Demo支付处理
- `Processing real vault payment` - 真实支付处理
- `Vault payment completed successfully` - 支付成功

## 生产环境注意事项

1. **安全性**: 确保vault token的安全存储和传输
2. **验证**: 验证用户对支付方式的所有权
3. **错误处理**: 实现完善的错误处理和重试机制
4. **监控**: 监控支付成功率和错误率
5. **合规性**: 遵守PCI DSS等安全标准

## 故障排除

### 常见问题

1. **Demo支付不工作**
   - 检查支付方式ID是否以 `demo_` 或 `test_` 开头
   - 查看后端日志确认处理类型

2. **真实支付失败**
   - 验证PayPal配置是否正确
   - 检查vault token是否有效
   - 查看PayPal API错误信息

3. **前端显示错误**
   - 检查后端服务是否运行
   - 验证API端点是否正确
   - 查看浏览器控制台错误

### 调试工具

- 测试页面: `/test-vault`
- 调试页面: `/debug-order`
- PayPal配置测试: `/test-paypal`

## 下一步

1. 集成真实的PayPal Vault API
2. 实现支付方式的增删改查
3. 添加支付方式验证
4. 实现用户认证和授权
5. 添加支付历史记录
