# PayPal Vault 支付完整指南

## 概述

本项目现在支持完整的PayPal Vault支付功能：
1. **真实Vault支付** - 使用真实PayPal API和vault tokens
2. **Token管理** - 完整的token生命周期管理
3. **自动清理** - 过期token的自动清理机制

## 核心功能

### Vault Token管理
- ✅ **Token创建**: 通过PayPal API创建vault setup tokens
- ✅ **Token存储**: 安全存储vault tokens到数据库
- ✅ **Token验证**: 验证token有效性和过期时间
- ✅ **Token清理**: 自动清理过期和无效tokens

### 支付功能
- ✅ **Vault支付**: 使用已保存的支付方式进行支付
- ✅ **默认支付方式**: 设置和管理默认支付方式
- ✅ **支付历史**: 完整的支付记录和追踪

## 使用流程

### 1. 设置新的支付方式

#### 步骤1: 访问设置页面
```
http://localhost:3000/vault/setup
```

#### 步骤2: 开始设置流程
1. 点击"开始设置支付方式"按钮
2. 系统调用 `/api/paypal/vault/setup` 创建setup token
3. 重定向到PayPal进行支付方式验证

#### 步骤3: 完成设置
1. 在PayPal页面完成支付方式验证
2. 系统自动调用 `/api/paypal/vault/complete` 保存vault token
3. 重定向回支付方式管理页面

### 2. 管理已保存的支付方式

#### 查看支付方式
```
http://localhost:3000/vault
```

#### 功能包括：
- 查看所有已保存的支付方式
- 设置默认支付方式
- 删除不需要的支付方式
- 查看token有效期和状态

### 3. 使用Vault支付

#### 在结账时选择已保存的支付方式
1. 访问结账页面
2. 选择已保存的支付方式
3. 系统使用vault token完成支付
4. 无需重新输入支付信息

## API端点

### 获取支付方式
```
GET /api/paypal/payment-methods/{customerId}
```
返回该客户的已保存支付方式列表（包含demo数据）

### Vault支付
```
POST /api/paypal/capture-vault-payment
Content-Type: application/json

{
  "amount": 10.00,
  "currency": "USD",
  "description": "Test Payment",
  "paymentMethodId": "demo_vault_method_123"
}
```

## Demo数据

系统提供以下demo支付方式：

### 支付方式1
- ID: `demo_vault_method_123`
- 类型: Credit Card
- 品牌: VISA
- 尾号: 1234
- 默认: 是

### 支付方式2
- ID: `demo_vault_method_456`
- 类型: Credit Card
- 品牌: MC (MasterCard)
- 尾号: 5678
- 默认: 否

## 数据库记录

### Demo支付
- 订单ID格式: `DEMO_VAULT_{timestamp}`
- 交易ID格式: `DEMO_VAULT_TXN_{timestamp}`
- 支付方式: `PAYPAL_VAULT_DEMO`
- 状态: `COMPLETED`

### 真实支付
- 使用PayPal返回的真实订单ID
- 使用PayPal返回的真实交易ID
- 支付方式: `PAYPAL_VAULT`
- 状态: 根据PayPal API返回

## 前端页面

### 测试页面 (`/test-vault`)
- 直接测试Vault支付API
- 支持Demo和真实支付测试
- 显示完整的响应数据

### 支付方式管理 (`/vault`)
- 查看已保存的支付方式
- 管理默认支付方式
- 删除支付方式

### 结账页面 (`/checkout`)
- 选择新支付方式或已保存支付方式
- 完整的支付流程
- 支持Vault支付

### 成功页面 (`/success`)
- 显示支付结果
- 区分普通支付和Vault支付
- 显示订单详情

## 错误处理

系统会处理以下错误情况：
- 无效的支付方式ID
- 无效的金额
- PayPal API错误
- 网络连接错误

## 日志

查看后端日志以了解支付处理过程：
```bash
# 启动后端时查看日志
cd backend
./mvnw spring-boot:run
```

关键日志信息：
- `Processing demo vault payment` - Demo支付处理
- `Processing real vault payment` - 真实支付处理
- `Vault payment completed successfully` - 支付成功

## 生产环境注意事项

1. **安全性**: 确保vault token的安全存储和传输
2. **验证**: 验证用户对支付方式的所有权
3. **错误处理**: 实现完善的错误处理和重试机制
4. **监控**: 监控支付成功率和错误率
5. **合规性**: 遵守PCI DSS等安全标准

## 故障排除

### 常见问题

1. **Demo支付不工作**
   - 检查支付方式ID是否以 `demo_` 或 `test_` 开头
   - 查看后端日志确认处理类型

2. **真实支付失败**
   - 验证PayPal配置是否正确
   - 检查vault token是否有效
   - 查看PayPal API错误信息

3. **前端显示错误**
   - 检查后端服务是否运行
   - 验证API端点是否正确
   - 查看浏览器控制台错误

### 调试工具

- 测试页面: `/test-vault`
- 调试页面: `/debug-order`
- PayPal配置测试: `/test-paypal`

## 下一步

1. 集成真实的PayPal Vault API
2. 实现支付方式的增删改查
3. 添加支付方式验证
4. 实现用户认证和授权
5. 添加支付历史记录
