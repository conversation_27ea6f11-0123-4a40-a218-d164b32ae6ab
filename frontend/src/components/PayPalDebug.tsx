'use client';

import { useState, useEffect } from 'react';

export default function PayPalDebug() {
  const [debugInfo, setDebugInfo] = useState<any>({});

  useEffect(() => {
    const checkPayPalSDK = () => {
      const info: any = {
        timestamp: new Date().toISOString(),
        clientId: process.env.NEXT_PUBLIC_PAYPAL_CLIENT_ID,
        windowPaypal: !!window.paypal,
        paypalMethods: window.paypal ? Object.keys(window.paypal) : null,
        buttonsAvailable: !!(window.paypal && window.paypal.Buttons),
        userAgent: navigator.userAgent,
        url: window.location.href,
      };

      if (window.paypal) {
        info.paypalVersion = window.paypal.version || 'unknown';
        info.paypalButtons = typeof window.paypal.Buttons;
      }

      setDebugInfo(info);
    };

    // 立即检查
    checkPayPalSDK();

    // 每秒检查一次，持续10秒
    const interval = setInterval(checkPayPalSDK, 1000);
    setTimeout(() => clearInterval(interval), 10000);

    return () => clearInterval(interval);
  }, []);

  const reloadPage = () => {
    window.location.reload();
  };

  const testPayPalSDK = () => {
    if (window.paypal && window.paypal.Buttons) {
      try {
        console.log('Testing PayPal Buttons creation...');
        const testConfig = {
          createOrder: () => Promise.resolve('test-order-id'),
          onApprove: () => Promise.resolve(),
        };
        
        // 尝试创建按钮但不渲染
        const buttons = window.paypal.Buttons(testConfig);
        console.log('PayPal Buttons created successfully:', buttons);
        alert('PayPal SDK 测试成功！');
      } catch (error) {
        console.error('PayPal SDK test failed:', error);
        alert('PayPal SDK 测试失败: ' + error);
      }
    } else {
      alert('PayPal SDK 未加载或 Buttons 方法不可用');
    }
  };

  return (
    <div className="bg-gray-100 p-4 rounded-lg">
      <h3 className="text-lg font-semibold mb-4">PayPal SDK 调试信息</h3>
      
      <div className="space-y-2 text-sm">
        <div><strong>时间戳:</strong> {debugInfo.timestamp}</div>
        <div><strong>Client ID:</strong> {debugInfo.clientId ? `${debugInfo.clientId.substring(0, 20)}...` : '未设置'}</div>
        <div><strong>window.paypal 存在:</strong> {debugInfo.windowPaypal ? '✅' : '❌'}</div>
        <div><strong>Buttons 方法可用:</strong> {debugInfo.buttonsAvailable ? '✅' : '❌'}</div>
        
        {debugInfo.paypalMethods && (
          <div><strong>PayPal 方法:</strong> {debugInfo.paypalMethods.join(', ')}</div>
        )}
        
        {debugInfo.paypalVersion && (
          <div><strong>PayPal 版本:</strong> {debugInfo.paypalVersion}</div>
        )}
        
        <div><strong>Buttons 类型:</strong> {debugInfo.paypalButtons || 'undefined'}</div>
      </div>

      <div className="mt-4 space-x-2">
        <button
          onClick={testPayPalSDK}
          className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
        >
          测试 PayPal SDK
        </button>
        
        <button
          onClick={reloadPage}
          className="bg-gray-600 text-white px-4 py-2 rounded hover:bg-gray-700"
        >
          重新加载页面
        </button>
      </div>

      <div className="mt-4">
        <h4 className="font-semibold mb-2">完整调试信息:</h4>
        <pre className="bg-white p-2 rounded text-xs overflow-auto max-h-40">
          {JSON.stringify(debugInfo, null, 2)}
        </pre>
      </div>
    </div>
  );
}
