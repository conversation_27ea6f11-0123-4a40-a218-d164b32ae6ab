'use client';

import { useState, useId, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { paypalSDK } from '@/utils/paypalSDK';
import {
  PayPalCreateOrderRequest,
  PayPalCreateOrderResponse,
  PayPalCaptureOrderRequest,
  PayPalCaptureOrderResponse,
  PayPalApprovalData,
  PayPalActions
} from '@/types/paypal';

interface PayPalButtonProps {
  amount: number;
  productId: string | number;
  onSuccess?: (orderData: PayPalCaptureOrderResponse) => void;
  onError?: (error: string) => void;
}

export default function PayPalButton({
  amount,
  productId,
  onSuccess,
  onError
}: PayPalButtonProps) {
  const router = useRouter();
  const containerId = useId();
  const [sdkReady, setSdkReady] = useState(false);
  const [buttonsRendered, setButtonsRendered] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [processing, setProcessing] = useState(false);
  const [currentStep, setCurrentStep] = useState<string>('');

  useEffect(() => {
    const initializeSDK = async () => {
      try {
        console.log('开始加载PayPal SDK...');
        await paypalSDK.loadSDK();
        console.log('PayPal SDK加载成功');
        setSdkReady(true);
        initializePayPal();
      } catch (error) {
        console.error('PayPal SDK加载失败:', error);
        setError('PayPal SDK加载失败，请刷新页面重试');
      }
    };

    initializeSDK();
  }, []);

  // 步骤1: 创建订单请求
  const createOrder = async (): Promise<string> => {
    try {
      setProcessing(true);
      setCurrentStep('步骤1: 创建订单请求');
      console.log('步骤1: 前端发起创建订单请求');

      // 步骤2: POST /v2/checkout/orders
      setCurrentStep('步骤2: 调用PayPal API创建订单');
      const orderRequest: PayPalCreateOrderRequest = {
        amount,
        currency: 'USD',
        description: `Product ${productId}`,
        productId,
      };

      const response = await fetch(`${process.env.BACKEND_API_URL || 'http://localhost:8080'}/api/paypal/create-order`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(orderRequest),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create order');
      }

      const data: PayPalCreateOrderResponse = await response.json();

      // 步骤3&4: 返回order ID
      setCurrentStep('步骤3&4: 返回Order ID');
      console.log('步骤3&4: 后端返回Order ID:', data.id);

      return data.id;
    } catch (err: any) {
      const errorMessage = err.message || 'Failed to create PayPal order. Please try again.';
      setError(errorMessage);
      onError?.(errorMessage);
      console.error('Error creating order:', err);
      throw err;
    } finally {
      setProcessing(false);
    }
  };

  // 步骤5: 使用PayPal JS SDK发起支付 (由PayPal SDK自动处理)
  // 步骤6: 支付完成重定向 (由PayPal SDK自动处理)

  // 步骤7: 通知支付完成，步骤8: 捕获支付
  const onApprove = async (data: PayPalApprovalData, actions: PayPalActions): Promise<void> => {
    try {
      setProcessing(true);
      setCurrentStep('步骤7: 通知支付完成');
      console.log('步骤7: PayPal通知支付完成，Order ID:', data.orderID);



      // 步骤8: 捕获支付 (PATCH /orders/{id}/capture)
      setCurrentStep('步骤8: 捕获支付');
      console.log('步骤8: 开始捕获支付');

      const captureRequest: PayPalCaptureOrderRequest = {
        orderId: data.orderID,
      };

      const response = await fetch(`${process.env.BACKEND_API_URL || 'http://localhost:8080'}/api/paypal/capture-order`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(captureRequest),
      });

      if (!response.ok) {
        const errorData = await response.json();

        // 特殊处理ORDER_NOT_APPROVED错误
        if (errorData.error && (errorData.error.includes('not approved') || errorData.error.includes('ORDER_NOT_APPROVED'))) {
          throw new Error('支付尚未完成批准，请确保您已在PayPal页面完成支付确认。');
        }

        throw new Error(errorData.error || 'Failed to capture order');
      }

      const orderData: PayPalCaptureOrderResponse = await response.json();
      console.log('支付捕获成功:', orderData);

      setCurrentStep('支付完成');

      // 调用成功回调
      onSuccess?.(orderData);

      // 重定向到成功页面
      router.push(`/success?orderId=${data.orderID}`);
    } catch (err: any) {
      const errorMessage = err.message || 'Payment failed. Please try again.';
      setError(errorMessage);
      onError?.(errorMessage);
      console.error('Error capturing order:', err);

      // 如果是订单未批准错误，给用户更明确的提示
      if (errorMessage.includes('not approved') || errorMessage.includes('ORDER_NOT_APPROVED')) {
        setError('支付未完成：请确保您已在PayPal页面完成支付确认，然后重试。');
      }
    } finally {
      setProcessing(false);
      setCurrentStep('');
    }
  };



  const initializePayPal = () => {
    console.log('Initializing PayPal...');
    console.log('window.paypal:', window.paypal);

    if (window.paypal && typeof window.paypal.Buttons === 'function') {
      try {
        console.log('PayPal SDK loaded successfully, creating buttons...');

        const paypalConfig = {
          createOrder,
          onApprove,
          onError: (err: any) => {
            const errorMessage = 'PayPal encountered an error. Please try again.';
            setError(errorMessage);
            onError?.(errorMessage);
            console.error('PayPal error:', err);
          },
          onCancel: () => {
            console.log('Payment cancelled');
            setProcessing(false);
            setCurrentStep('');
          },
          style: {
            layout: 'vertical' as const,
            color: 'blue' as const,
            shape: 'rect' as const,
            label: 'pay' as const,
          },
        };

        window.paypal.Buttons(paypalConfig).render(`#paypal-button-container-${containerId}`);
        setButtonsRendered(true);
        console.log('PayPal按钮初始化完成');
      } catch (error) {
        console.error('Error rendering PayPal buttons:', error);
        setError('Failed to load PayPal. Please refresh and try again.');
      }
    } else {
      console.error('PayPal SDK not loaded properly');
      console.log('Available PayPal methods:', window.paypal ? Object.keys(window.paypal) : 'PayPal not available');
      setError('PayPal SDK failed to load. Please check your internet connection and try again.');
    }
  };



  const clearError = () => {
    setError(null);
  };

  const retryPayPalInit = () => {
    setError(null);
    setSdkReady(false);
    setButtonsRendered(false);

    // 重新加载页面以重新初始化PayPal SDK
    window.location.reload();
  };

  return (
    <div className="paypal-button-wrapper">

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          <div className="flex justify-between items-center">
            <span>{error}</span>
            <div className="flex space-x-2">
              {error.includes('SDK') && (
                <button
                  onClick={retryPayPalInit}
                  className="text-red-700 hover:text-red-900 font-medium text-sm underline"
                >
                  重试
                </button>
              )}
              <button
                onClick={clearError}
                className="text-red-700 hover:text-red-900 font-bold"
              >
                ×
              </button>
            </div>
          </div>
        </div>
      )}

      {processing && (
        <div className="bg-blue-100 border border-blue-400 text-blue-700 px-4 py-3 rounded mb-4">
          <div className="flex items-center">
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-700 mr-2"></div>
            <span>{currentStep || 'Processing payment...'}</span>
          </div>
        </div>
      )}

      <div id={`paypal-button-container-${containerId}`} className="mt-4">
        {!sdkReady && (
          <div className="flex items-center justify-center p-4 bg-gray-100 rounded">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600 mr-2"></div>
            <span>Loading PayPal...</span>
          </div>
        )}
      </div>

      {sdkReady && !buttonsRendered && (
        <div className="mt-4 text-center text-gray-500">
          Initializing PayPal buttons...
        </div>
      )}
    </div>
  );
}
