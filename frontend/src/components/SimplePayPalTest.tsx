'use client';

import { useState, useEffect, useId } from 'react';
import { paypalSDK } from '@/utils/paypalSDK';

export default function SimplePayPalTest() {
  const containerId = useId();
  const [sdkReady, setSdkReady] = useState(false);
  const [buttonsRendered, setButtonsRendered] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [logs, setLogs] = useState<string[]>([]);

  const addLog = (message: string) => {
    const timestamp = new Date().toLocaleTimeString();
    const logMessage = `[${timestamp}] ${message}`;
    setLogs(prev => [...prev, logMessage]);
    console.log(logMessage);
  };

  useEffect(() => {
    const initializeSDK = async () => {
      try {
        addLog('开始加载PayPal SDK...');
        await paypalSDK.loadSDK();
        addLog('PayPal SDK加载成功');
        setSdkReady(true);
        createButtons();
      } catch (error) {
        addLog(`PayPal SDK加载失败: ${error}`);
        setError('PayPal SDK加载失败，请刷新页面重试');
      }
    };

    initializeSDK();
  }, []);

  const createButtons = () => {
    if (!window.paypal || typeof window.paypal.Buttons !== 'function') {
      addLog('PayPal SDK未准备好');
      setError('PayPal SDK未准备好');
      return;
    }

    try {
      addLog('开始创建PayPal按钮...');
      
      const paypalConfig = {
        createOrder: () => {
          addLog('createOrder被调用');
          return Promise.resolve('test-order-id');
        },
        onApprove: (data: any) => {
          addLog(`onApprove被调用，订单ID: ${data.orderID}`);
          return Promise.resolve();
        },
        onError: (err: any) => {
          addLog(`PayPal错误: ${err}`);
        },
        onCancel: () => {
          addLog('支付被取消');
        },
        style: {
          layout: 'vertical' as const,
          color: 'blue' as const,
          shape: 'rect' as const,
          label: 'pay' as const,
        },
      };

      const containerSelector = `#simple-paypal-container-${containerId}`;
      addLog(`渲染到容器: ${containerSelector}`);
      
      window.paypal.Buttons(paypalConfig).render(containerSelector);
      setButtonsRendered(true);
      addLog('PayPal按钮创建成功');
    } catch (error) {
      addLog(`创建PayPal按钮失败: ${error}`);
      setError(`创建PayPal按钮失败: ${error}`);
    }
  };

  const clearLogs = () => {
    setLogs([]);
  };

  const retry = () => {
    setError(null);
    setSdkReady(false);
    setButtonsRendered(false);
    setLogs([]);
    window.location.reload();
  };

  return (
    <div className="bg-white border border-gray-200 rounded-lg p-6">
      <h3 className="text-lg font-semibold mb-4">简化PayPal测试</h3>
      
      {/* 状态指示器 */}
      <div className="grid grid-cols-3 gap-4 mb-4">
        <div className={`p-3 rounded ${sdkReady ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-600'}`}>
          <div className="text-sm font-medium">SDK状态</div>
          <div className="text-xs">{sdkReady ? '✅ 已加载' : '⏳ 加载中'}</div>
        </div>
        
        <div className={`p-3 rounded ${buttonsRendered ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-600'}`}>
          <div className="text-sm font-medium">按钮状态</div>
          <div className="text-xs">{buttonsRendered ? '✅ 已创建' : '⏳ 等待中'}</div>
        </div>
        
        <div className={`p-3 rounded ${error ? 'bg-red-100 text-red-800' : 'bg-green-100 text-green-800'}`}>
          <div className="text-sm font-medium">错误状态</div>
          <div className="text-xs">{error ? '❌ 有错误' : '✅ 正常'}</div>
        </div>
      </div>

      {/* 错误显示 */}
      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          <div className="flex justify-between items-center">
            <span className="text-sm">{error}</span>
            <button
              onClick={retry}
              className="text-red-700 hover:text-red-900 font-medium text-sm underline"
            >
              重试
            </button>
          </div>
        </div>
      )}

      {/* PayPal按钮容器 */}
      <div className="mb-4">
        <div id={`simple-paypal-container-${containerId}`} className="min-h-[60px] border border-gray-200 rounded p-2">
          {!sdkReady && (
            <div className="flex items-center justify-center h-16 text-gray-500">
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600 mr-2"></div>
              正在加载PayPal SDK...
            </div>
          )}
          {sdkReady && !buttonsRendered && (
            <div className="flex items-center justify-center h-16 text-gray-500">
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-green-600 mr-2"></div>
              正在创建PayPal按钮...
            </div>
          )}
        </div>
      </div>

      {/* 控制按钮 */}
      <div className="flex space-x-2 mb-4">
        <button
          onClick={clearLogs}
          className="bg-gray-600 text-white px-3 py-1 rounded text-sm hover:bg-gray-700"
        >
          清除日志
        </button>
        
        <button
          onClick={retry}
          className="bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700"
        >
          重新加载
        </button>
      </div>

      {/* 日志显示 */}
      <div className="bg-gray-900 text-green-400 p-3 rounded text-xs">
        <div className="font-semibold mb-2">实时日志:</div>
        <div className="max-h-32 overflow-y-auto">
          {logs.length === 0 ? (
            <div className="text-gray-500">暂无日志...</div>
          ) : (
            logs.map((log, index) => (
              <div key={index} className="font-mono text-xs">
                {log}
              </div>
            ))
          )}
        </div>
      </div>
    </div>
  );
}
