'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { VaultedPaymentMethod, PayPalCaptureOrderResponse } from '@/types/paypal';

interface VaultedPaymentButtonProps {
  amount: number;
  productId: string | number;
  paymentMethod: VaultedPaymentMethod;
  onSuccess?: (orderData: PayPalCaptureOrderResponse) => void;
  onError?: (error: string) => void;
}

export default function VaultedPaymentButton({
  amount,
  productId,
  paymentMethod,
  onSuccess,
  onError
}: VaultedPaymentButtonProps) {
  const router = useRouter();
  const [processing, setProcessing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [currentStep, setCurrentStep] = useState<string>('');

  // 使用已保存的支付方式进行支付
  const handleVaultedPayment = async () => {
    try {
      setProcessing(true);
      setError(null);
      setCurrentStep('使用已保存的支付方式处理支付');

      console.log('使用Vault支付方式:', paymentMethod);

      // 直接使用vault支付方式创建并捕获支付
      const vaultPaymentRequest = {
        amount,
        currency: 'USD',
        description: `Product ${productId} - Vault Payment`,
        paymentMethodId: paymentMethod.paymentMethodId,
      };

      const response = await fetch(`${process.env.BACKEND_API_URL || 'http://localhost:8080'}/api/paypal/capture-vault-payment`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(vaultPaymentRequest),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to process vault payment');
      }

      const paymentData: PayPalCaptureOrderResponse = await response.json();
      console.log('Vault支付成功:', paymentData);

      setCurrentStep('支付完成');

      // 调用成功回调
      onSuccess?.(paymentData);

      // 重定向到成功页面，标记为vault支付
      router.push(`/success?orderId=${paymentData.id}&vaulted=true`);

    } catch (err: any) {
      const errorMessage = err.message || 'Vault payment failed. Please try again.';
      setError(errorMessage);
      onError?.(errorMessage);
      console.error('Error processing vault payment:', err);
    } finally {
      setProcessing(false);
      setCurrentStep('');
    }
  };

  const clearError = () => {
    setError(null);
  };

  return (
    <div className="vaulted-payment-wrapper">
      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          <div className="flex justify-between items-center">
            <span>{error}</span>
            <button
              onClick={clearError}
              className="text-red-700 hover:text-red-900 font-bold"
            >
              ×
            </button>
          </div>
        </div>
      )}

      {processing && (
        <div className="bg-blue-100 border border-blue-400 text-blue-700 px-4 py-3 rounded mb-4">
          <div className="flex items-center">
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-700 mr-2"></div>
            <span>{currentStep || 'Processing payment...'}</span>
          </div>
        </div>
      )}

      <div className="border border-gray-200 rounded-lg p-4 bg-white">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center">
            <div className="w-10 h-6 bg-blue-600 rounded text-white text-xs flex items-center justify-center font-bold mr-3">
              {paymentMethod.brand}
            </div>
            <div>
              <div className="font-medium text-gray-900">
                {paymentMethod.paymentType} {paymentMethod.lastFour}
              </div>
              {paymentMethod.isDefault && (
                <div className="text-xs text-green-600">默认支付方式</div>
              )}
            </div>
          </div>
          <div className="text-right">
            <div className="text-lg font-bold text-gray-900">${amount}</div>
          </div>
        </div>

        <button
          onClick={handleVaultedPayment}
          disabled={processing}
          className={`w-full py-3 px-4 rounded-lg font-medium transition-colors ${
            processing
              ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
              : 'bg-blue-600 text-white hover:bg-blue-700'
          }`}
        >
          {processing ? '处理中...' : '使用此支付方式付款'}
        </button>
      </div>

      <div className="mt-2 text-xs text-gray-500 text-center">
        安全快速支付 • 无需重新输入信息
      </div>
    </div>
  );
}
