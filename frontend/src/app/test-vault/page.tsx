'use client';

import { useState } from 'react';
import Link from 'next/link';

export default function TestVault() {
  const [amount, setAmount] = useState('10.00');
  const [paymentMethodId, setPaymentMethodId] = useState('demo_vault_method_123');
  const [result, setResult] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const testVaultPayment = async () => {
    setLoading(true);
    setError(null);
    setResult(null);

    try {
      const response = await fetch(`${process.env.BACKEND_API_URL || 'http://localhost:8080'}/api/paypal/capture-vault-payment`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          amount: parseFloat(amount),
          currency: 'USD',
          description: 'Test Vault Payment',
          paymentMethodId,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Vault payment failed');
      }

      const data = await response.json();
      setResult(data);
      console.log('Vault payment successful:', data);
    } catch (err: any) {
      setError(err.message || 'Failed to process vault payment');
      console.error('Vault payment error:', err);
    } finally {
      setLoading(false);
    }
  };

  const testRealVaultPayment = async () => {
    setLoading(true);
    setError(null);
    setResult(null);

    try {
      // Use a real-looking payment method ID to trigger real PayPal API call
      const realPaymentMethodId = 'real_vault_token_456';
      
      const response = await fetch(`${process.env.BACKEND_API_URL || 'http://localhost:8080'}/api/paypal/capture-vault-payment`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          amount: parseFloat(amount),
          currency: 'USD',
          description: 'Real Vault Payment Test',
          paymentMethodId: realPaymentMethodId,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Real vault payment failed');
      }

      const data = await response.json();
      setResult(data);
      console.log('Real vault payment successful:', data);
    } catch (err: any) {
      setError(err.message || 'Failed to process real vault payment');
      console.error('Real vault payment error:', err);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="max-w-2xl mx-auto">
      <div className="mb-6">
        <Link href="/" className="text-blue-600 hover:text-blue-800 flex items-center">
          <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clipRule="evenodd" />
          </svg>
          返回首页
        </Link>
      </div>

      <h1 className="text-3xl font-bold text-gray-900 mb-8">Vault 支付测试</h1>

      <div className="bg-white rounded-lg shadow-md p-6 mb-6">
        <h2 className="text-xl font-semibold mb-4">测试 Vault 支付</h2>
        
        <div className="space-y-4">
          <div>
            <label htmlFor="amount" className="block text-sm font-medium text-gray-700 mb-2">
              支付金额 (USD)
            </label>
            <input
              type="number"
              id="amount"
              value={amount}
              onChange={(e) => setAmount(e.target.value)}
              step="0.01"
              min="0.01"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
          
          <div>
            <label htmlFor="paymentMethodId" className="block text-sm font-medium text-gray-700 mb-2">
              支付方式 ID
            </label>
            <input
              type="text"
              id="paymentMethodId"
              value={paymentMethodId}
              onChange={(e) => setPaymentMethodId(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
            <p className="text-xs text-gray-500 mt-1">
              以 "demo_" 或 "test_" 开头的ID将使用模拟支付，其他ID将尝试真实PayPal API
            </p>
          </div>
          
          <div className="flex space-x-4">
            <button
              onClick={testVaultPayment}
              disabled={loading}
              className="flex-1 bg-green-600 text-white py-3 px-4 rounded-md hover:bg-green-700 disabled:bg-gray-400 font-medium"
            >
              {loading ? '处理中...' : '测试 Demo Vault 支付'}
            </button>
            
            <button
              onClick={testRealVaultPayment}
              disabled={loading}
              className="flex-1 bg-blue-600 text-white py-3 px-4 rounded-md hover:bg-blue-700 disabled:bg-gray-400 font-medium"
            >
              {loading ? '处理中...' : '测试真实 Vault 支付'}
            </button>
          </div>
        </div>
      </div>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
          <h3 className="font-semibold">错误</h3>
          <p>{error}</p>
        </div>
      )}

      {result && (
        <div className={`border px-4 py-3 rounded mb-6 ${
          result.demo ? 'bg-yellow-100 border-yellow-400 text-yellow-700' : 'bg-green-100 border-green-400 text-green-700'
        }`}>
          <h3 className="font-semibold mb-2">
            {result.demo ? 'Demo Vault 支付成功！' : 'Vault 支付成功！'}
          </h3>
          <div className="space-y-2 text-sm">
            <div><strong>订单ID:</strong> {result.id}</div>
            <div><strong>状态:</strong> {result.status}</div>
            <div><strong>支付方式:</strong> {result.payment_method}</div>
            <div><strong>支付方式ID:</strong> {result.payment_method_id}</div>
            {result.demo && <div><strong>类型:</strong> 演示支付</div>}
            {result.purchase_units && result.purchase_units[0] && result.purchase_units[0].amount && (
              <div><strong>金额:</strong> ${result.purchase_units[0].amount.value} {result.purchase_units[0].amount.currency_code}</div>
            )}
          </div>
          
          <div className="mt-4">
            <h4 className="font-medium mb-2">完整响应数据:</h4>
            <pre className="bg-gray-100 p-2 rounded text-xs overflow-auto max-h-40">
              {JSON.stringify(result, null, 2)}
            </pre>
          </div>
        </div>
      )}

      <div className="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-6">
        <h3 className="text-lg font-semibold text-blue-800 mb-3">支付类型说明</h3>
        <div className="space-y-2 text-sm text-blue-700">
          <div className="flex items-start">
            <span className="font-semibold mr-2">Demo支付:</span>
            <span>使用以 "demo_" 或 "test_" 开头的支付方式ID，仅在数据库中创建记录</span>
          </div>
          <div className="flex items-start">
            <span className="font-semibold mr-2">真实支付:</span>
            <span>使用其他格式的支付方式ID，会调用真实的PayPal API</span>
          </div>
        </div>
      </div>

      <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6">
        <h3 className="text-lg font-semibold text-yellow-800 mb-3">注意事项</h3>
        <div className="space-y-2 text-sm text-yellow-700">
          <div className="flex items-start">
            <span className="font-semibold mr-2">1.</span>
            <span>真实支付需要有效的PayPal Vault Token</span>
          </div>
          <div className="flex items-start">
            <span className="font-semibold mr-2">2.</span>
            <span>Demo支付适用于开发和测试环境</span>
          </div>
          <div className="flex items-start">
            <span className="font-semibold mr-2">3.</span>
            <span>生产环境应该使用真实的Vault Token</span>
          </div>
        </div>
      </div>
    </div>
  );
}
