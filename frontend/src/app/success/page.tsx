'use client';

import { useEffect, useState, Suspense } from 'react';
import { useSearchParams } from 'next/navigation';
import Link from 'next/link';

function SuccessContent() {
  const searchParams = useSearchParams();
  const orderId = searchParams.get('orderId');
  const isVaulted = searchParams.get('vaulted') === 'true';
  
  const [orderDetails, setOrderDetails] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (orderId) {
      fetchOrderDetails(orderId);
    } else {
      setLoading(false);
      setError('未提供订单ID');
    }
  }, [orderId]);

  const fetchOrderDetails = async (id: string) => {
    try {
      const response = await fetch(`${process.env.BACKEND_API_URL || 'http://localhost:8080'}/api/paypal/order-details/${id}`);
      
      if (!response.ok) {
        throw new Error('获取订单详情失败');
      }
      
      const data = await response.json();
      setOrderDetails(data);
    } catch (err: any) {
      setError(err.message || '获取订单详情失败');
      console.error('Error fetching order details:', err);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-600"></div>
        <span className="ml-2">加载订单详情...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center">
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
          <h2 className="text-xl font-bold mb-2">错误</h2>
          <p>{error}</p>
        </div>
        <Link href="/" className="text-blue-600 hover:text-blue-800 font-medium">
          返回首页
        </Link>
      </div>
    );
  }

  return (
    <div className="max-w-2xl mx-auto">
      {/* 成功消息 */}
      <div className="text-center mb-8">
        <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
          <svg className="w-8 h-8 text-green-600" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
          </svg>
        </div>
        <h1 className="text-3xl font-bold text-gray-900 mb-2">支付成功！</h1>
        <p className="text-gray-600">
          感谢您的购买，您的支付已成功处理。
          {isVaulted && ' 使用了已保存的支付方式，快速便捷！'}
        </p>
      </div>

      {/* 订单详情 */}
      <div className="bg-white rounded-lg shadow-md p-6 mb-6">
        <h2 className="text-xl font-semibold mb-4">订单详情</h2>
        
        {orderDetails && (
          <div className="space-y-3">
            <div className="flex justify-between">
              <span className="text-gray-600">订单ID:</span>
              <span className="font-mono text-sm">{orderDetails.id}</span>
            </div>
            
            <div className="flex justify-between">
              <span className="text-gray-600">状态:</span>
              <span className={`px-2 py-1 rounded text-sm font-medium ${
                orderDetails.status === 'COMPLETED' 
                  ? 'bg-green-100 text-green-800' 
                  : 'bg-yellow-100 text-yellow-800'
              }`}>
                {orderDetails.status === 'COMPLETED' ? '已完成' : orderDetails.status}
              </span>
            </div>
            
            {orderDetails.purchase_units && orderDetails.purchase_units[0] && (
              <div className="flex justify-between">
                <span className="text-gray-600">金额:</span>
                <span className="font-semibold text-green-600">
                  ${orderDetails.purchase_units[0].amount.value} {orderDetails.purchase_units[0].amount.currency_code}
                </span>
              </div>
            )}
            
            {orderDetails.create_time && (
              <div className="flex justify-between">
                <span className="text-gray-600">创建时间:</span>
                <span className="text-sm">
                  {new Date(orderDetails.create_time).toLocaleString('zh-CN')}
                </span>
              </div>
            )}
            
            {orderDetails.update_time && (
              <div className="flex justify-between">
                <span className="text-gray-600">更新时间:</span>
                <span className="text-sm">
                  {new Date(orderDetails.update_time).toLocaleString('zh-CN')}
                </span>
              </div>
            )}
          </div>
        )}
      </div>

      {/* Vault 信息 */}
      {isVaulted ? (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
          <div className="flex items-start">
            <svg className="w-5 h-5 text-blue-600 mr-2 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M18 8a6 6 0 01-7.743 5.743L10 14l-0.257-0.257A6 6 0 1118 8zM10 2a1 1 0 011 1v1a1 1 0 11-2 0V3a1 1 0 011-1zm-7 7a1 1 0 000 2h1a1 1 0 100-2H3zm14 0a1 1 0 100 2h1a1 1 0 100-2h-1zM5.05 6.464A1 1 0 106.464 5.05l-.707-.707a1 1 0 00-1.414 1.414l.707.707zm9.9 0l.707-.707a1 1 0 00-1.414-1.414l-.707.707a1 1 0 001.414 1.414z" clipRule="evenodd" />
            </svg>
            <div>
              <h3 className="font-medium text-blue-800">使用了已保存的支付方式</h3>
              <p className="text-sm text-blue-700 mt-1">
                您使用了之前保存的支付方式完成了此次购买，快速又安全！
              </p>
            </div>
          </div>
        </div>
      ) : (
        <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
          <div className="flex items-start">
            <svg className="w-5 h-5 text-green-600 mr-2 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
            </svg>
            <div>
              <h3 className="font-medium text-green-800">支付方式已保存</h3>
              <p className="text-sm text-green-700 mt-1">
                您的支付方式已安全保存，下次购买时可以直接使用，无需重新输入信息。
              </p>
            </div>
          </div>
        </div>
      )}

      {/* 操作按钮 */}
      <div className="flex flex-col sm:flex-row gap-4 justify-center">
        <Link 
          href="/" 
          className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors font-medium text-center"
        >
          继续购物
        </Link>
        <Link 
          href="/orders" 
          className="bg-gray-600 text-white px-6 py-3 rounded-lg hover:bg-gray-700 transition-colors font-medium text-center"
        >
          查看所有订单
        </Link>
        <Link 
          href="/vault" 
          className="bg-green-600 text-white px-6 py-3 rounded-lg hover:bg-green-700 transition-colors font-medium text-center"
        >
          管理支付方式
        </Link>
      </div>

      {/* 支付流程完成提示 */}
      <div className="mt-8 bg-gray-50 rounded-lg p-6">
        <h3 className="text-lg font-semibold mb-4 text-center">支付流程已完成</h3>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 text-sm">
          <div className="text-center">
            <div className="w-8 h-8 bg-green-600 text-white rounded-full flex items-center justify-center mx-auto mb-2">
              <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="font-medium">订单创建</div>
            <div className="text-gray-600">已完成</div>
          </div>
          <div className="text-center">
            <div className="w-8 h-8 bg-green-600 text-white rounded-full flex items-center justify-center mx-auto mb-2">
              <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="font-medium">PayPal处理</div>
            <div className="text-gray-600">已完成</div>
          </div>
          <div className="text-center">
            <div className="w-8 h-8 bg-green-600 text-white rounded-full flex items-center justify-center mx-auto mb-2">
              <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="font-medium">用户支付</div>
            <div className="text-gray-600">已完成</div>
          </div>
          <div className="text-center">
            <div className="w-8 h-8 bg-green-600 text-white rounded-full flex items-center justify-center mx-auto mb-2">
              <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="font-medium">支付捕获</div>
            <div className="text-gray-600">已完成</div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default function Success() {
  return (
    <Suspense fallback={
      <div className="flex items-center justify-center min-h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-600"></div>
        <span className="ml-2">Loading...</span>
      </div>
    }>
      <SuccessContent />
    </Suspense>
  );
}
