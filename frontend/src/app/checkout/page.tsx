'use client';

import { useEffect, useState, Suspense } from 'react';
import { useSearchParams } from 'next/navigation';
import Link from 'next/link';
import PayPalButton from '@/components/PayPalButton';
import PayPalDebug from '@/components/PayPalDebug';
import SimplePayPalTest from '@/components/SimplePayPalTest';
import FallbackPayPalButton from '@/components/FallbackPayPalButton';
import PayPalDiagnostics from '@/components/PayPalDiagnostics';
import PayPalSDKTester from '@/components/PayPalSDKTester';

function CheckoutContent() {
  const searchParams = useSearchParams();
  const productId = searchParams.get('productId');
  const price = searchParams.get('price');
  const name = searchParams.get('name');

  const [loading, setLoading] = useState(true);
  const [product, setProduct] = useState<any>(null);

  useEffect(() => {
    // 设置产品信息
    if (productId && price && name) {
      setProduct({
        id: productId,
        name: decodeURIComponent(name),
        price: parseFloat(price),
        description: `这是${decodeURIComponent(name)}的详细描述`,
      });
    }

    setLoading(false);
  }, [productId, price, name]);

  const handlePaymentSuccess = (orderData: any) => {
    console.log('Payment successful:', orderData);
    // 重定向到成功页面
    window.location.href = `/success?orderId=${orderData.id}`;
  };

  const handlePaymentError = (error: string) => {
    console.error('Payment error:', error);
    alert(`支付失败: ${error}`);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <span className="ml-2">加载中...</span>
      </div>
    );
  }

  if (!product) {
    return (
      <div className="text-center py-12">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">产品信息不完整</h2>
        <p className="text-gray-600 mb-6">请从产品页面重新开始购买流程。</p>
        <Link href="/" className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors">
          返回首页
        </Link>
      </div>
    );
  }

  return (
    <div className="max-w-2xl mx-auto">
      <div className="mb-6">
        <Link href="/" className="text-blue-600 hover:text-blue-800 flex items-center">
          <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clipRule="evenodd" />
          </svg>
          返回首页
        </Link>
      </div>

      <h1 className="text-3xl font-bold text-gray-900 mb-8">结账</h1>

      <div className="bg-white rounded-lg shadow-md p-6 mb-6">
        <h2 className="text-xl font-semibold mb-4">订单详情</h2>

        <div className="border-b border-gray-200 pb-4 mb-4">
          <div className="flex justify-between items-center">
            <div>
              <h3 className="font-medium text-gray-900">{product.name}</h3>
              <p className="text-sm text-gray-600">{product.description}</p>
            </div>
            <div className="text-right">
              <div className="text-lg font-semibold text-gray-900">${product.price}</div>
            </div>
          </div>
        </div>

        <div className="flex justify-between items-center text-lg font-semibold">
          <span>总计</span>
          <span>${product.price}</span>
        </div>
      </div>

      <div className="bg-white rounded-lg shadow-md p-6">
        <h2 className="text-xl font-semibold mb-4">支付方式</h2>

        <div className="mb-6">
          <div className="flex items-center mb-4">
            <input
              type="radio"
              id="paypal"
              name="paymentMethod"
              value="paypal"
              checked={true}
              readOnly
              className="mr-3"
            />
            <label htmlFor="paypal" className="font-medium">PayPal</label>
          </div>
          <p className="text-sm text-gray-600 ml-6">
            使用PayPal安全支付，支持信用卡、借记卡和PayPal余额。
          </p>
        </div>

        {/* PayPal支付按钮 */}
        <div className="payment-section space-y-6">
          <div>
            <h4 className="font-medium mb-2">主要PayPal按钮</h4>
            <PayPalButton
              amount={product.price}
              productId={product.id}
              onSuccess={handlePaymentSuccess}
              onError={handlePaymentError}
            />
          </div>

          {/* 备用PayPal按钮 */}
          <div className="border-t pt-6">
            <h4 className="font-medium mb-2">备用PayPal按钮（如果上面的按钮不工作）</h4>
            <FallbackPayPalButton
              amount={product.price}
              productId={product.id}
              onSuccess={handlePaymentSuccess}
              onError={handlePaymentError}
            />
          </div>
        </div>
      </div>

      {/* 开发环境下显示调试信息 */}
      {process.env.NODE_ENV === 'development' && (
        <div className="mt-6 space-y-6">
          <PayPalSDKTester />
          <PayPalDiagnostics />
          <SimplePayPalTest />
          <PayPalDebug />
        </div>
      )}

      <div className="mt-6 text-center text-sm text-gray-500">
        <p>点击支付即表示您同意我们的服务条款和隐私政策</p>
      </div>
    </div>
  );
}

export default function CheckoutPage() {
  return (
    <Suspense fallback={
      <div className="flex items-center justify-center min-h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <span className="ml-2">加载中...</span>
      </div>
    }>
      <CheckoutContent />
    </Suspense>
  );
}
