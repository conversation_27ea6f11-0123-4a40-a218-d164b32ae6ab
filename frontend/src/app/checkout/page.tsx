'use client';

import { useEffect, useState, Suspense } from 'react';
import { useSearchParams } from 'next/navigation';
import Link from 'next/link';
import PayPalButton from '@/components/PayPalButton';
import VaultedPaymentButton from '@/components/VaultedPaymentButton';
import { VaultedPaymentMethod } from '@/types/paypal';

function CheckoutContent() {
  const searchParams = useSearchParams();
  const productId = searchParams.get('productId');
  const price = searchParams.get('price');
  const name = searchParams.get('name');
  
  const [loading, setLoading] = useState(true);
  const [product, setProduct] = useState<any>(null);
  const [vaultedMethods, setVaultedMethods] = useState<VaultedPaymentMethod[]>([]);
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState<'new' | 'vaulted'>('new');
  const [selectedVaultedMethod, setSelectedVaultedMethod] = useState<VaultedPaymentMethod | null>(null);
  const [customerId] = useState('default_customer'); // 在实际应用中，这应该来自用户认证

  useEffect(() => {
    // 设置产品信息
    if (productId && price && name) {
      setProduct({
        id: productId,
        name: decodeURIComponent(name),
        price: parseFloat(price),
        description: `这是${decodeURIComponent(name)}的详细描述`,
      });
    }
    
    // 加载已保存的支付方式
    loadVaultedPaymentMethods();
    
    setLoading(false);
  }, [productId, price, name]);

  const loadVaultedPaymentMethods = async () => {
    try {
      const response = await fetch(`${process.env.BACKEND_API_URL || 'http://localhost:8080'}/api/paypal/payment-methods/${customerId}`);
      if (response.ok) {
        const methods = await response.json();
        setVaultedMethods(methods);
        
        // 如果有默认支付方式，自动选择它
        const defaultMethod = methods.find((method: VaultedPaymentMethod) => method.isDefault);
        if (defaultMethod) {
          setSelectedVaultedMethod(defaultMethod);
          setSelectedPaymentMethod('vaulted');
        }
      }
    } catch (error) {
      console.error('Error loading vaulted payment methods:', error);
    }
  };

  const handlePaymentSuccess = (orderData: any) => {
    console.log('Payment successful:', orderData);
    // 重新加载已保存的支付方式（可能有新的）
    loadVaultedPaymentMethods();
  };

  const handlePaymentError = (error: string) => {
    console.error('Payment error:', error);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <span className="ml-2">Loading...</span>
      </div>
    );
  }

  if (!product) {
    return (
      <div className="text-center">
        <h2 className="text-2xl font-bold mb-6 text-gray-900">商品未找到</h2>
        <p className="text-gray-600 mb-6">请选择一个有效的商品进行购买。</p>
        <Link href="/" className="text-blue-600 hover:text-blue-800 font-medium">
          返回商品列表
        </Link>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto">
      <div className="mb-6">
        <Link href="/" className="text-blue-600 hover:text-blue-800 flex items-center">
          <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clipRule="evenodd" />
          </svg>
          返回商品列表
        </Link>
      </div>

      <h1 className="text-3xl font-bold text-gray-900 mb-8">结账</h1>
      
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* 商品信息 */}
        <div className="bg-white rounded-lg shadow-md p-6">
          <h2 className="text-xl font-semibold mb-4">订单详情</h2>
          <div className="border-b border-gray-200 pb-4 mb-4">
            <div className="flex justify-between items-start">
              <div>
                <h3 className="font-medium text-gray-900">{product.name}</h3>
                <p className="text-sm text-gray-600 mt-1">{product.description}</p>
              </div>
              <div className="text-right">
                <div className="text-lg font-bold text-gray-900">${product.price}</div>
              </div>
            </div>
          </div>
          
          <div className="flex justify-between items-center text-lg font-bold">
            <span>总计:</span>
            <span className="text-green-600">${product.price}</span>
          </div>
        </div>

        {/* 支付方式选择 */}
        <div className="bg-white rounded-lg shadow-md p-6">
          <h2 className="text-xl font-semibold mb-4">选择支付方式</h2>
          
          {/* 支付方式选项 */}
          <div className="space-y-4 mb-6">
            {vaultedMethods.length > 0 && (
              <div>
                <label className="flex items-center">
                  <input
                    type="radio"
                    name="paymentMethod"
                    value="vaulted"
                    checked={selectedPaymentMethod === 'vaulted'}
                    onChange={(e) => setSelectedPaymentMethod(e.target.value as 'vaulted')}
                    className="mr-3"
                  />
                  <span className="font-medium">使用已保存的支付方式</span>
                </label>
                
                {selectedPaymentMethod === 'vaulted' && (
                  <div className="mt-3 ml-6 space-y-2">
                    {vaultedMethods.map((method) => (
                      <label key={method.id} className="flex items-center">
                        <input
                          type="radio"
                          name="vaultedMethod"
                          value={method.id}
                          checked={selectedVaultedMethod?.id === method.id}
                          onChange={() => setSelectedVaultedMethod(method)}
                          className="mr-3"
                        />
                        <div className="flex items-center">
                          <div className="w-8 h-5 bg-blue-600 rounded text-white text-xs flex items-center justify-center font-bold mr-2">
                            {method.brand}
                          </div>
                          <span className="text-sm">
                            {method.paymentType} {method.lastFour}
                            {method.isDefault && <span className="text-green-600 ml-2">(默认)</span>}
                          </span>
                        </div>
                      </label>
                    ))}
                  </div>
                )}
              </div>
            )}
            
            <label className="flex items-center">
              <input
                type="radio"
                name="paymentMethod"
                value="new"
                checked={selectedPaymentMethod === 'new'}
                onChange={(e) => setSelectedPaymentMethod(e.target.value as 'new')}
                className="mr-3"
              />
              <span className="font-medium">使用新的支付方式</span>
            </label>
          </div>

          {/* 支付按钮 */}
          <div className="payment-section">
            {selectedPaymentMethod === 'vaulted' && selectedVaultedMethod ? (
              <VaultedPaymentButton
                amount={product.price}
                productId={product.id}
                paymentMethod={selectedVaultedMethod}
                onSuccess={handlePaymentSuccess}
                onError={handlePaymentError}
              />
            ) : (
              <div>
                <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded">
                  <p className="text-sm text-blue-700">
                    使用新的支付方式将会自动保存您的支付信息，以便下次快速支付。
                  </p>
                </div>
                <PayPalButton
                  amount={product.price}
                  productId={product.id}
                  customerId={customerId}
                  enableVault={true}
                  onSuccess={handlePaymentSuccess}
                  onError={handlePaymentError}
                />
              </div>
            )}
          </div>
        </div>
      </div>

      {/* 支付流程说明 */}
      <div className="mt-8 bg-gray-50 rounded-lg p-6">
        <h3 className="text-lg font-semibold mb-4">支付流程说明</h3>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 text-sm">
          <div className="flex items-start">
            <div className="w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-xs font-bold mr-3 mt-0.5">1</div>
            <div>
              <div className="font-medium">创建订单</div>
              <div className="text-gray-600">前端发起订单创建请求</div>
            </div>
          </div>
          <div className="flex items-start">
            <div className="w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-xs font-bold mr-3 mt-0.5">2</div>
            <div>
              <div className="font-medium">PayPal处理</div>
              <div className="text-gray-600">调用PayPal API创建订单</div>
            </div>
          </div>
          <div className="flex items-start">
            <div className="w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-xs font-bold mr-3 mt-0.5">3</div>
            <div>
              <div className="font-medium">用户支付</div>
              <div className="text-gray-600">PayPal SDK处理支付</div>
            </div>
          </div>
          <div className="flex items-start">
            <div className="w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-xs font-bold mr-3 mt-0.5">4</div>
            <div>
              <div className="font-medium">捕获支付</div>
              <div className="text-gray-600">后端确认并捕获支付</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default function Checkout() {
  return (
    <Suspense fallback={
      <div className="flex items-center justify-center min-h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <span className="ml-2">Loading...</span>
      </div>
    }>
      <CheckoutContent />
    </Suspense>
  );
}
