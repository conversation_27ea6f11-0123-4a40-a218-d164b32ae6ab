'use client';

import { useEffect, useState } from 'react';
import Link from 'next/link';

interface Order {
  id: number;
  paypalOrderId: string;
  amount: number;
  currency: string;
  status: string;
  createdAt: string;
  refunded: boolean;
  refundAmount?: number;
}

export default function Orders() {
  const [orders, setOrders] = useState<Order[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [refundingOrder, setRefundingOrder] = useState<string | null>(null);
  const [refundAmount, setRefundAmount] = useState<string>('');
  const [refundError, setRefundError] = useState<string | null>(null);
  const [refundSuccess, setRefundSuccess] = useState<string | null>(null);

  useEffect(() => {
    fetchOrders();
  }, []);

  const fetchOrders = async () => {
    try {
      const response = await fetch(`${process.env.BACKEND_API_URL || 'http://localhost:18080'}/api/paypal/orders`);
      
      if (!response.ok) {
        throw new Error('获取订单失败');
      }
      
      const data = await response.json();
      setOrders(data);
    } catch (err: any) {
      setError(err.message || '获取订单失败');
      console.error('Error fetching orders:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleRefund = async (orderId: string) => {
    setRefundingOrder(orderId);
    setRefundError(null);
    setRefundSuccess(null);
  };

  const submitRefund = async (orderId: string) => {
    try {
      setRefundError(null);
      setRefundSuccess(null);
      
      if (!refundAmount || isNaN(parseFloat(refundAmount)) || parseFloat(refundAmount) <= 0) {
        setRefundError('请输入有效的退款金额');
        return;
      }
      
      const response = await fetch(`${process.env.BACKEND_API_URL || 'http://localhost:18080'}/api/paypal/refund`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          captureId: orderId, // 在实际应用中，这应该是capture ID
          amount: parseFloat(refundAmount),
          currency: 'USD',
          reason: '客户申请退款',
        }),
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || '退款失败');
      }
      
      const data = await response.json();
      setRefundSuccess('退款处理成功');
      setRefundingOrder(null);
      setRefundAmount('');
      
      // 刷新订单列表以显示更新的退款状态
      fetchOrders();
    } catch (err: any) {
      setRefundError(err.message || '退款处理失败');
      console.error('Error processing refund:', err);
    }
  };

  const cancelRefund = () => {
    setRefundingOrder(null);
    setRefundAmount('');
    setRefundError(null);
    setRefundSuccess(null);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <span className="ml-2">加载订单...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center">
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
          <h2 className="text-xl font-bold mb-2">错误</h2>
          <p>{error}</p>
        </div>
        <Link href="/" className="text-blue-600 hover:text-blue-800 font-medium">
          返回首页
        </Link>
      </div>
    );
  }

  return (
    <div className="max-w-6xl mx-auto">
      <div className="mb-6">
        <Link href="/" className="text-blue-600 hover:text-blue-800 flex items-center">
          <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clipRule="evenodd" />
          </svg>
          返回首页
        </Link>
      </div>

      <h1 className="text-3xl font-bold text-gray-900 mb-8">订单管理</h1>
      
      {refundSuccess && (
        <div className="bg-green-100 border border-green-200 text-green-700 px-4 py-3 rounded mb-6">
          <div className="flex items-center">
            <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
            </svg>
            {refundSuccess}
          </div>
        </div>
      )}
      
      {orders.length === 0 ? (
        <div className="text-center py-12">
          <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg className="w-8 h-8 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
              <path d="M9 2a1 1 0 000 2h2a1 1 0 100-2H9z" />
              <path fillRule="evenodd" d="M4 5a2 2 0 012-2v1a2 2 0 00-2 2v6a2 2 0 002 2h8a2 2 0 002-2V6a2 2 0 00-2-2V3a2 2 0 012 2v8a2 2 0 01-2 2H6a2 2 0 01-2-2V5z" clipRule="evenodd" />
            </svg>
          </div>
          <h2 className="text-xl font-semibold text-gray-900 mb-2">暂无订单</h2>
          <p className="text-gray-600 mb-6">您还没有任何订单记录。</p>
          <Link 
            href="/" 
            className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors font-medium"
          >
            开始购物
          </Link>
        </div>
      ) : (
        <div className="bg-white rounded-lg shadow-md overflow-hidden">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    订单ID
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    PayPal订单ID
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    创建时间
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    金额
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    状态
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    操作
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {orders.map((order) => (
                  <tr key={order.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                      #{order.id}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 font-mono">
                      {order.paypalOrderId}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {new Date(order.createdAt).toLocaleString('zh-CN')}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      <span className="font-semibold">${order.amount}</span>
                      <span className="text-gray-500 ml-1">{order.currency}</span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex flex-col">
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                          order.status === 'COMPLETED' 
                            ? 'bg-green-100 text-green-800' 
                            : order.status === 'PENDING'
                            ? 'bg-yellow-100 text-yellow-800'
                            : 'bg-gray-100 text-gray-800'
                        }`}>
                          {order.status === 'COMPLETED' ? '已完成' : order.status}
                        </span>
                        {order.refunded && (
                          <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-orange-100 text-orange-800 mt-1">
                            已退款 ${order.refundAmount || 0}
                          </span>
                        )}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm">
                      {refundingOrder === order.paypalOrderId ? (
                        <div className="space-y-2">
                          <div className="flex items-center space-x-2">
                            <input
                              type="number"
                              value={refundAmount}
                              onChange={(e) => setRefundAmount(e.target.value)}
                              placeholder="退款金额"
                              className="border border-gray-300 rounded px-2 py-1 w-24 text-sm"
                              step="0.01"
                              min="0.01"
                              max={order.amount}
                            />
                            <button
                              onClick={() => submitRefund(order.paypalOrderId)}
                              className="bg-blue-600 text-white px-3 py-1 rounded text-xs hover:bg-blue-700 transition-colors"
                            >
                              确认
                            </button>
                            <button
                              onClick={cancelRefund}
                              className="bg-gray-300 text-gray-700 px-3 py-1 rounded text-xs hover:bg-gray-400 transition-colors"
                            >
                              取消
                            </button>
                          </div>
                          {refundError && (
                            <p className="text-red-500 text-xs">{refundError}</p>
                          )}
                        </div>
                      ) : (
                        <div className="flex space-x-2">
                          <button
                            onClick={() => handleRefund(order.paypalOrderId)}
                            disabled={order.refunded || order.status !== 'COMPLETED'}
                            className={`px-3 py-1 rounded text-xs font-medium transition-colors ${
                              order.refunded || order.status !== 'COMPLETED'
                                ? 'bg-gray-200 text-gray-500 cursor-not-allowed'
                                : 'bg-yellow-500 text-white hover:bg-yellow-600'
                            }`}
                          >
                            {order.refunded ? '已退款' : '申请退款'}
                          </button>
                          <Link
                            href={`/success?orderId=${order.paypalOrderId}`}
                            className="bg-blue-100 text-blue-700 px-3 py-1 rounded text-xs hover:bg-blue-200 transition-colors"
                          >
                            查看详情
                          </Link>
                        </div>
                      )}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )}

      {/* 退款说明 */}
      <div className="mt-8 bg-yellow-50 border border-yellow-200 rounded-lg p-6">
        <h3 className="text-lg font-semibold text-yellow-800 mb-3">退款说明</h3>
        <div className="space-y-2 text-sm text-yellow-700">
          <div className="flex items-start">
            <svg className="w-4 h-4 mr-2 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
            </svg>
            <span>只有状态为"已完成"的订单才能申请退款</span>
          </div>
          <div className="flex items-start">
            <svg className="w-4 h-4 mr-2 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
            </svg>
            <span>支持部分退款和全额退款</span>
          </div>
          <div className="flex items-start">
            <svg className="w-4 h-4 mr-2 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
            </svg>
            <span>退款将通过PayPal处理，通常在3-5个工作日内到账</span>
          </div>
        </div>
      </div>
    </div>
  );
}
