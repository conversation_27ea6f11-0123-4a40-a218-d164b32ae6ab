import type { Metadata } from 'next';
import './globals.css';

export const metadata: Metadata = {
  title: 'PayPal Demo - 支付集成演示',
  description: 'PayPal Orders API v2 + Vault 支付集成演示应用',
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="zh-CN">
      <body>
        <main className="min-h-screen bg-gray-50">
          <header className="bg-white shadow-sm border-b">
            <div className="container mx-auto px-4 py-4">
              <nav className="flex items-center justify-between">
                <h1 className="text-2xl font-bold text-gray-900">PayPal Demo</h1>
                <ul className="flex space-x-6">
                  <li>
                    <a href="/" className="text-blue-600 hover:text-blue-800 font-medium">
                      首页
                    </a>
                  </li>
                  <li>
                    <a href="/checkout" className="text-blue-600 hover:text-blue-800 font-medium">
                      结账
                    </a>
                  </li>
                  <li>
                    <a href="/orders" className="text-blue-600 hover:text-blue-800 font-medium">
                      订单管理
                    </a>
                  </li>
                  <li>
                    <a href="/vault" className="text-blue-600 hover:text-blue-800 font-medium">
                      支付方式
                    </a>
                  </li>
                  <li>
                    <a href="/test-paypal" className="text-orange-600 hover:text-orange-800 font-medium">
                      测试
                    </a>
                  </li>
                </ul>
              </nav>
            </div>
          </header>
          <div className="container mx-auto px-4 py-8">
            {children}
          </div>
        </main>
      </body>
    </html>
  );
}
