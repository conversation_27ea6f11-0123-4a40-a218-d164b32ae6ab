'use client';

import { useEffect, useState } from 'react';
import Link from 'next/link';
import { VaultedPaymentMethod } from '@/types/paypal';

export default function VaultPage() {
  const [paymentMethods, setPaymentMethods] = useState<VaultedPaymentMethod[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [customerId] = useState('default_customer'); // 在实际应用中，这应该来自用户认证

  useEffect(() => {
    fetchPaymentMethods();
  }, []);

  const fetchPaymentMethods = async () => {
    try {
      setLoading(true);
      const response = await fetch(`${process.env.BACKEND_API_URL || 'http://localhost:8080'}/api/paypal/payment-methods/${customerId}`);
      
      if (!response.ok) {
        throw new Error('获取支付方式失败');
      }
      
      const data = await response.json();
      setPaymentMethods(data);
    } catch (err: any) {
      setError(err.message || '获取支付方式失败');
      console.error('Error fetching payment methods:', err);
    } finally {
      setLoading(false);
    }
  };

  const setAsDefault = async (methodId: number) => {
    try {
      // 这里应该调用后端API来设置默认支付方式
      // 为了演示，我们只在前端更新状态
      setPaymentMethods(methods => 
        methods.map(method => ({
          ...method,
          isDefault: method.id === methodId
        }))
      );
      
      console.log(`设置支付方式 ${methodId} 为默认`);
    } catch (err) {
      console.error('Error setting default payment method:', err);
    }
  };

  const deletePaymentMethod = async (methodId: number) => {
    if (!confirm('确定要删除这个支付方式吗？')) {
      return;
    }
    
    try {
      // 这里应该调用后端API来删除支付方式
      // 为了演示，我们只在前端更新状态
      setPaymentMethods(methods => 
        methods.filter(method => method.id !== methodId)
      );
      
      console.log(`删除支付方式 ${methodId}`);
    } catch (err) {
      console.error('Error deleting payment method:', err);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <span className="ml-2">加载支付方式...</span>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto">
      <div className="mb-6">
        <Link href="/" className="text-blue-600 hover:text-blue-800 flex items-center">
          <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clipRule="evenodd" />
          </svg>
          返回首页
        </Link>
      </div>

      <div className="flex items-center justify-between mb-8">
        <h1 className="text-3xl font-bold text-gray-900">已保存的支付方式</h1>
        <Link 
          href="/checkout" 
          className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
        >
          添加新支付方式
        </Link>
      </div>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
          {error}
        </div>
      )}

      {paymentMethods.length === 0 ? (
        <div className="text-center py-12">
          <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg className="w-8 h-8 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
              <path d="M4 4a2 2 0 00-2 2v1h16V6a2 2 0 00-2-2H4z" />
              <path fillRule="evenodd" d="M18 9H2v5a2 2 0 002 2h12a2 2 0 002-2V9zM4 13a1 1 0 011-1h1a1 1 0 110 2H5a1 1 0 01-1-1zm5-1a1 1 0 100 2h1a1 1 0 100-2H9z" clipRule="evenodd" />
            </svg>
          </div>
          <h2 className="text-xl font-semibold text-gray-900 mb-2">暂无保存的支付方式</h2>
          <p className="text-gray-600 mb-6">
            完成首次购买后，您的支付方式将自动保存在这里，方便下次快速支付。
          </p>
          <Link 
            href="/" 
            className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors font-medium"
          >
            开始购物
          </Link>
        </div>
      ) : (
        <div className="space-y-4">
          {paymentMethods.map((method) => (
            <div key={method.id} className="bg-white border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow">
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <div className="w-12 h-8 bg-blue-600 rounded text-white text-xs flex items-center justify-center font-bold mr-4">
                    {method.brand}
                  </div>
                  <div>
                    <div className="font-medium text-gray-900">
                      {method.paymentType} 
                      <span className="text-gray-500 ml-2">•••• {method.lastFour}</span>
                    </div>
                    <div className="text-sm text-gray-500">
                      添加于 {new Date(method.createdAt).toLocaleDateString('zh-CN')}
                    </div>
                    {method.isDefault && (
                      <div className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 mt-1">
                        <svg className="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                        </svg>
                        默认支付方式
                      </div>
                    )}
                  </div>
                </div>
                
                <div className="flex items-center space-x-2">
                  {!method.isDefault && (
                    <button
                      onClick={() => setAsDefault(method.id)}
                      className="text-blue-600 hover:text-blue-800 text-sm font-medium"
                    >
                      设为默认
                    </button>
                  )}
                  <button
                    onClick={() => deletePaymentMethod(method.id)}
                    className="text-red-600 hover:text-red-800 text-sm font-medium"
                  >
                    删除
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Vault 功能说明 */}
      <div className="mt-8 bg-blue-50 border border-blue-200 rounded-lg p-6">
        <h3 className="text-lg font-semibold text-blue-800 mb-3">关于 PayPal Vault</h3>
        <div className="space-y-2 text-sm text-blue-700">
          <div className="flex items-start">
            <svg className="w-4 h-4 mr-2 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
            </svg>
            <span>安全存储：您的支付信息通过 PayPal 的安全系统加密存储</span>
          </div>
          <div className="flex items-start">
            <svg className="w-4 h-4 mr-2 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
            </svg>
            <span>快速支付：后续购买时无需重新输入支付信息</span>
          </div>
          <div className="flex items-start">
            <svg className="w-4 h-4 mr-2 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
            </svg>
            <span>完全控制：您可以随时删除或修改已保存的支付方式</span>
          </div>
        </div>
      </div>

      {/* Vault 流程说明 */}
      <div className="mt-6 bg-gray-50 rounded-lg p-6">
        <h3 className="text-lg font-semibold mb-4">Vault 存储流程</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
          <div className="text-center">
            <div className="w-10 h-10 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-2">
              <span className="text-purple-600 font-bold">1</span>
            </div>
            <div className="font-medium">用户首次支付</div>
            <div className="text-gray-600 mt-1">完成首次 PayPal 支付</div>
          </div>
          <div className="text-center">
            <div className="w-10 h-10 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-2">
              <span className="text-purple-600 font-bold">2</span>
            </div>
            <div className="font-medium">通过Vault存储支付方式</div>
            <div className="text-gray-600 mt-1">系统自动保存支付信息</div>
          </div>
          <div className="text-center">
            <div className="w-10 h-10 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-2">
              <span className="text-purple-600 font-bold">3</span>
            </div>
            <div className="font-medium">获得payment_token</div>
            <div className="text-gray-600 mt-1">用于后续快速支付</div>
          </div>
        </div>
        <div className="mt-4 text-center">
          <div className="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-2">
            <span className="text-green-600 font-bold">4</span>
          </div>
          <div className="font-medium">后续支付直接使用token</div>
          <div className="text-gray-600 mt-1">无需重新输入支付信息，一键完成支付</div>
        </div>
      </div>
    </div>
  );
}
