'use client';

import { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import Link from 'next/link';

export default function VaultSetupPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [customerId] = useState('default_customer'); // In real app, get from auth
  
  // Check if we're completing a setup
  const setupToken = searchParams.get('token');
  const isComplete = searchParams.get('complete') === 'true';

  useEffect(() => {
    if (setupToken && !isComplete) {
      completeVaultSetup();
    }
  }, [setupToken, isComplete]);

  const startVaultSetup = async () => {
    setLoading(true);
    setError(null);

    try {
      const response = await fetch(`${process.env.BACKEND_API_URL || 'http://localhost:8080'}/api/paypal/vault/setup`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          customerId,
          returnUrl: `${window.location.origin}/vault/setup?complete=true`,
          cancelUrl: `${window.location.origin}/vault`
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create vault setup');
      }

      const data = await response.json();
      
      // Redirect to PayPal for setup
      if (data.approvalUrl) {
        window.location.href = data.approvalUrl;
      } else {
        throw new Error('No approval URL received');
      }

    } catch (err: any) {
      setError(err.message || 'Failed to start vault setup');
      console.error('Vault setup error:', err);
    } finally {
      setLoading(false);
    }
  };

  const completeVaultSetup = async () => {
    if (!setupToken) return;

    setLoading(true);
    setError(null);

    try {
      const response = await fetch(`${process.env.BACKEND_API_URL || 'http://localhost:8080'}/api/paypal/vault/complete`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          setupToken,
          customerId,
          isDefault: true // Make first payment method default
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to complete vault setup');
      }

      const data = await response.json();
      console.log('Vault setup completed:', data);
      
      // Redirect to vault page with success message
      router.push('/vault?setup=success');

    } catch (err: any) {
      setError(err.message || 'Failed to complete vault setup');
      console.error('Vault completion error:', err);
    } finally {
      setLoading(false);
    }
  };

  if (setupToken && !isComplete) {
    return (
      <div className="max-w-2xl mx-auto">
        <div className="text-center py-12">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <h2 className="text-xl font-semibold text-gray-900 mb-2">完成支付方式设置</h2>
          <p className="text-gray-600">正在保存您的支付方式...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-2xl mx-auto">
      <div className="mb-6">
        <Link href="/vault" className="text-blue-600 hover:text-blue-800 flex items-center">
          <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clipRule="evenodd" />
          </svg>
          返回支付方式管理
        </Link>
      </div>

      <h1 className="text-3xl font-bold text-gray-900 mb-8">添加新的支付方式</h1>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
          <h3 className="font-semibold">错误</h3>
          <p>{error}</p>
        </div>
      )}

      <div className="bg-white rounded-lg shadow-md p-6 mb-6">
        <h2 className="text-xl font-semibold mb-4">设置 PayPal Vault 支付方式</h2>
        
        <div className="space-y-4 mb-6">
          <div className="flex items-start">
            <svg className="w-5 h-5 text-green-500 mr-3 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
            </svg>
            <div>
              <div className="font-medium">安全存储</div>
              <div className="text-sm text-gray-600">您的支付信息将通过PayPal的安全系统加密存储</div>
            </div>
          </div>
          
          <div className="flex items-start">
            <svg className="w-5 h-5 text-green-500 mr-3 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
            </svg>
            <div>
              <div className="font-medium">快速支付</div>
              <div className="text-sm text-gray-600">后续购买时无需重新输入支付信息</div>
            </div>
          </div>
          
          <div className="flex items-start">
            <svg className="w-5 h-5 text-green-500 mr-3 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
            </svg>
            <div>
              <div className="font-medium">完全控制</div>
              <div className="text-sm text-gray-600">您可以随时删除或修改已保存的支付方式</div>
            </div>
          </div>
        </div>

        <button
          onClick={startVaultSetup}
          disabled={loading}
          className={`w-full py-3 px-4 rounded-lg font-medium transition-colors ${
            loading
              ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
              : 'bg-blue-600 text-white hover:bg-blue-700'
          }`}
        >
          {loading ? '正在设置...' : '开始设置支付方式'}
        </button>
      </div>

      <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6">
        <h3 className="text-lg font-semibold text-yellow-800 mb-3">设置流程</h3>
        <div className="space-y-2 text-sm text-yellow-700">
          <div className="flex items-start">
            <span className="font-semibold mr-2">1.</span>
            <span>点击"开始设置支付方式"按钮</span>
          </div>
          <div className="flex items-start">
            <span className="font-semibold mr-2">2.</span>
            <span>您将被重定向到PayPal进行支付方式验证</span>
          </div>
          <div className="flex items-start">
            <span className="font-semibold mr-2">3.</span>
            <span>完成验证后，支付方式将自动保存</span>
          </div>
          <div className="flex items-start">
            <span className="font-semibold mr-2">4.</span>
            <span>您将返回到支付方式管理页面</span>
          </div>
        </div>
      </div>
    </div>
  );
}
