'use client';

import { useState, useEffect } from 'react';
import <PERSON>ript from 'next/script';
import Link from 'next/link';

export default function TestPayPalPage() {
  const [sdkLoaded, setSdkLoaded] = useState(false);
  const [buttonsCreated, setButtonsCreated] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [logs, setLogs] = useState<string[]>([]);

  const addLog = (message: string) => {
    const timestamp = new Date().toLocaleTimeString();
    setLogs(prev => [...prev, `[${timestamp}] ${message}`]);
    console.log(message);
  };

  useEffect(() => {
    addLog('Component mounted');
    addLog(`Client ID: ${process.env.NEXT_PUBLIC_PAYPAL_CLIENT_ID ? 'Set' : 'Not set'}`);
  }, []);

  const handleScriptLoad = () => {
    addLog('PayPal SDK script loaded');
    setSdkLoaded(true);
    
    // 检查PayPal对象
    if (window.paypal) {
      addLog('window.paypal is available');
      addLog(`PayPal methods: ${Object.keys(window.paypal).join(', ')}`);
      
      if (typeof window.paypal.Buttons === 'function') {
        addLog('PayPal.Buttons is a function - SDK loaded correctly');
        createTestButtons();
      } else {
        addLog('PayPal.Buttons is not a function');
        setError('PayPal.Buttons is not available');
      }
    } else {
      addLog('window.paypal is not available');
      setError('PayPal SDK not loaded properly');
    }
  };

  const createTestButtons = () => {
    try {
      addLog('Creating PayPal buttons...');
      
      const paypalConfig = {
        createOrder: () => {
          addLog('createOrder called');
          return Promise.resolve('test-order-id');
        },
        onApprove: (data: any) => {
          addLog(`onApprove called with order ID: ${data.orderID}`);
          return Promise.resolve();
        },
        onError: (err: any) => {
          addLog(`PayPal error: ${err}`);
        },
        onCancel: () => {
          addLog('Payment cancelled');
        },
        style: {
          layout: 'vertical' as const,
          color: 'blue' as const,
          shape: 'rect' as const,
          label: 'pay' as const,
        },
      };

      window.paypal.Buttons(paypalConfig).render('#test-paypal-container');
      setButtonsCreated(true);
      addLog('PayPal buttons created and rendered successfully');
    } catch (error) {
      addLog(`Error creating PayPal buttons: ${error}`);
      setError(`Failed to create PayPal buttons: ${error}`);
    }
  };

  const clearLogs = () => {
    setLogs([]);
  };

  const reloadPage = () => {
    window.location.reload();
  };

  return (
    <div className="max-w-4xl mx-auto p-6">
      <div className="mb-6">
        <Link href="/" className="text-blue-600 hover:text-blue-800 flex items-center">
          <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clipRule="evenodd" />
          </svg>
          返回首页
        </Link>
      </div>

      <h1 className="text-3xl font-bold text-gray-900 mb-8">PayPal SDK 测试</h1>

      {/* 状态指示器 */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
        <div className={`p-4 rounded-lg ${sdkLoaded ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-600'}`}>
          <div className="font-semibold">SDK 加载状态</div>
          <div>{sdkLoaded ? '✅ 已加载' : '⏳ 加载中...'}</div>
        </div>
        
        <div className={`p-4 rounded-lg ${buttonsCreated ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-600'}`}>
          <div className="font-semibold">按钮创建状态</div>
          <div>{buttonsCreated ? '✅ 已创建' : '⏳ 等待中...'}</div>
        </div>
        
        <div className={`p-4 rounded-lg ${error ? 'bg-red-100 text-red-800' : 'bg-gray-100 text-gray-600'}`}>
          <div className="font-semibold">错误状态</div>
          <div>{error ? '❌ 有错误' : '✅ 正常'}</div>
        </div>
      </div>

      {/* 错误显示 */}
      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
          <h3 className="font-semibold">错误信息</h3>
          <p>{error}</p>
        </div>
      )}

      {/* PayPal 按钮容器 */}
      <div className="bg-white border border-gray-200 rounded-lg p-6 mb-6">
        <h2 className="text-xl font-semibold mb-4">PayPal 测试按钮</h2>
        <div id="test-paypal-container" className="min-h-[100px]">
          {!sdkLoaded && (
            <div className="flex items-center justify-center h-24 text-gray-500">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600 mr-2"></div>
              正在加载 PayPal SDK...
            </div>
          )}
        </div>
      </div>

      {/* 控制按钮 */}
      <div className="flex space-x-4 mb-6">
        <button
          onClick={clearLogs}
          className="bg-gray-600 text-white px-4 py-2 rounded hover:bg-gray-700"
        >
          清除日志
        </button>
        
        <button
          onClick={reloadPage}
          className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
        >
          重新加载页面
        </button>
      </div>

      {/* 日志显示 */}
      <div className="bg-gray-900 text-green-400 p-4 rounded-lg">
        <h3 className="text-lg font-semibold mb-2">实时日志</h3>
        <div className="max-h-60 overflow-y-auto">
          {logs.length === 0 ? (
            <div className="text-gray-500">暂无日志...</div>
          ) : (
            logs.map((log, index) => (
              <div key={index} className="text-sm font-mono">
                {log}
              </div>
            ))
          )}
        </div>
      </div>

      {/* PayPal SDK 脚本 */}
      <Script
        src={`https://www.paypal.com/sdk/js?client-id=${process.env.NEXT_PUBLIC_PAYPAL_CLIENT_ID}&currency=USD&intent=capture&components=buttons&debug=true`}
        onLoad={handleScriptLoad}
        onError={(e) => {
          addLog(`PayPal SDK failed to load: ${e}`);
          setError('PayPal SDK 加载失败');
        }}
        strategy="afterInteractive"
      />
    </div>
  );
}
