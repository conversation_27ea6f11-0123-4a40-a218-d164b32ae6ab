// PayPal SDK 管理器
class PayPalSDKManager {
  private static instance: PayPalSDKManager;
  private isLoading = false;
  private isLoaded = false;
  private loadPromise: Promise<void> | null = null;
  private callbacks: Array<() => void> = [];

  private constructor() {}

  static getInstance(): PayPalSDKManager {
    if (!PayPalSDKManager.instance) {
      PayPalSDKManager.instance = new PayPalSDKManager();
    }
    return PayPalSDKManager.instance;
  }

  async loadSDK(): Promise<void> {
    // 如果已经加载，直接返回
    if (this.isLoaded && window.paypal && typeof window.paypal.Buttons === 'function') {
      return Promise.resolve();
    }

    // 如果正在加载，返回现有的Promise
    if (this.isLoading && this.loadPromise) {
      return this.loadPromise;
    }

    // 开始加载
    this.isLoading = true;
    this.loadPromise = this.createLoadPromise();

    try {
      await this.loadPromise;
      this.isLoaded = true;
      this.notifyCallbacks();
    } catch (error) {
      this.isLoading = false;
      this.loadPromise = null;
      throw error;
    } finally {
      this.isLoading = false;
    }
  }

  private createLoadPromise(): Promise<void> {
    return new Promise((resolve, reject) => {
      // 检查是否已经有脚本标签
      const existingScript = document.querySelector('script[src*="paypal.com/sdk/js"]');
      if (existingScript) {
        // 如果脚本已存在，等待加载完成
        if (window.paypal && typeof window.paypal.Buttons === 'function') {
          resolve();
          return;
        }

        // 监听现有脚本的加载
        existingScript.addEventListener('load', () => {
          this.waitForPayPal().then(resolve).catch(reject);
        });
        existingScript.addEventListener('error', reject);
        return;
      }

      // 创建新的脚本标签
      const script = document.createElement('script');
      script.src = `https://www.paypal.com/sdk/js?client-id=${process.env.NEXT_PUBLIC_PAYPAL_CLIENT_ID}&currency=USD&intent=capture&components=buttons&debug=true`;
      script.async = true;

      script.onload = () => {
        this.waitForPayPal().then(resolve).catch(reject);
      };

      script.onerror = () => {
        reject(new Error('Failed to load PayPal SDK'));
      };

      document.head.appendChild(script);
    });
  }

  private waitForPayPal(): Promise<void> {
    return new Promise((resolve, reject) => {
      let retryCount = 0;
      const maxRetries = 20;

      const checkPayPal = () => {
        if (window.paypal && typeof window.paypal.Buttons === 'function') {
          resolve();
        } else if (retryCount < maxRetries) {
          retryCount++;
          setTimeout(checkPayPal, 100);
        } else {
          reject(new Error('PayPal SDK failed to initialize'));
        }
      };

      checkPayPal();
    });
  }

  onReady(callback: () => void): void {
    if (this.isLoaded && window.paypal && typeof window.paypal.Buttons === 'function') {
      callback();
    } else {
      this.callbacks.push(callback);
    }
  }

  private notifyCallbacks(): void {
    this.callbacks.forEach(callback => callback());
    this.callbacks = [];
  }

  isSDKReady(): boolean {
    return this.isLoaded && window.paypal && typeof window.paypal.Buttons === 'function';
  }

  getLoadingState(): { isLoading: boolean; isLoaded: boolean } {
    return {
      isLoading: this.isLoading,
      isLoaded: this.isLoaded && window.paypal && typeof window.paypal.Buttons === 'function'
    };
  }
}

export const paypalSDK = PayPalSDKManager.getInstance();
