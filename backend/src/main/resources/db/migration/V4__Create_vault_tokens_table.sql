-- Create vault_tokens table for storing PayPal vault tokens
CREATE TABLE vault_tokens (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    customer_id VARCHAR(255) NOT NULL,
    vault_token VARCHAR(500) NOT NULL UNIQUE,
    payment_source_type VARCHAR(50),
    card_brand VARCHAR(50),
    card_last_four VARCHAR(4),
    card_expiry_month VARCHAR(2),
    card_expiry_year VARCHAR(4),
    paypal_email VARCHAR(255),
    is_default BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    expires_at TIMESTAMP NULL,
    last_used_at TIMESTAMP NULL,
    
    INDEX idx_customer_id (customer_id),
    INDEX idx_vault_token (vault_token),
    INDEX idx_customer_active (customer_id, is_active),
    INDEX idx_customer_default (customer_id, is_default),
    INDEX idx_expires_at (expires_at)
);

-- Add comments for documentation
ALTER TABLE vault_tokens 
COMMENT = 'Stores PayPal vault tokens for saved payment methods';

ALTER TABLE vault_tokens 
MODIFY COLUMN customer_id VARCHAR(255) NOT NULL COMMENT 'Customer identifier',
MODIFY COLUMN vault_token VARCHAR(500) NOT NULL COMMENT 'PayPal vault token',
MODIFY COLUMN payment_source_type VARCHAR(50) COMMENT 'Type of payment source (CARD, PAYPAL, BANK_ACCOUNT)',
MODIFY COLUMN card_brand VARCHAR(50) COMMENT 'Card brand (VISA, MASTERCARD, AMEX, etc.)',
MODIFY COLUMN card_last_four VARCHAR(4) COMMENT 'Last four digits of card',
MODIFY COLUMN card_expiry_month VARCHAR(2) COMMENT 'Card expiry month (MM)',
MODIFY COLUMN card_expiry_year VARCHAR(4) COMMENT 'Card expiry year (YYYY)',
MODIFY COLUMN paypal_email VARCHAR(255) COMMENT 'PayPal account email for PayPal payment source',
MODIFY COLUMN is_default BOOLEAN DEFAULT FALSE COMMENT 'Whether this is the default payment method',
MODIFY COLUMN is_active BOOLEAN DEFAULT TRUE COMMENT 'Whether the token is active',
MODIFY COLUMN expires_at TIMESTAMP NULL COMMENT 'Token expiration timestamp',
MODIFY COLUMN last_used_at TIMESTAMP NULL COMMENT 'Last time the token was used for payment';
