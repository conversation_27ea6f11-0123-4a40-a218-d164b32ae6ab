package com.example.paypal.scheduler;

import com.example.paypal.service.VaultService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * Scheduled task to clean up expired vault tokens
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class VaultTokenCleanupScheduler {
    
    private final VaultService vaultService;
    
    /**
     * Clean up expired vault tokens daily at 2 AM
     */
    @Scheduled(cron = "0 0 2 * * ?")
    public void cleanupExpiredTokens() {
        try {
            log.info("Starting scheduled cleanup of expired vault tokens");
            
            int cleanedCount = vaultService.cleanupExpiredTokens();
            
            log.info("Scheduled cleanup completed. Cleaned up {} expired vault tokens", cleanedCount);
            
        } catch (Exception e) {
            log.error("Error during scheduled vault token cleanup", e);
        }
    }
    
    /**
     * Log vault token statistics every hour
     */
    @Scheduled(fixedRate = 3600000) // Every hour
    public void logVaultTokenStats() {
        try {
            // This is a simple health check - in a real application,
            // you might want to collect and log more detailed statistics
            log.debug("Vault token cleanup scheduler is running");
            
        } catch (Exception e) {
            log.error("Error during vault token stats logging", e);
        }
    }
}
