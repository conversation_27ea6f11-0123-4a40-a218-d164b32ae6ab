package com.example.paypal.controller;

import com.example.paypal.model.Order;
import com.example.paypal.model.Refund;
import com.example.paypal.model.VaultedPaymentMethod;
import com.example.paypal.service.PayPalService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/api/paypal")
@RequiredArgsConstructor
@Slf4j
public class PayPalController {

    private final PayPalService payPalService;

    @PostMapping("/create-order")
    public ResponseEntity<Map<String, Object>> createOrder(@RequestBody Map<String, Object> request) {
        try {
            BigDecimal amount = new BigDecimal(request.get("amount").toString());
            String currency = request.getOrDefault("currency", "USD").toString();
            String description = request.getOrDefault("description", "PayPal Demo Order").toString();

            Map<String, Object> orderResult = payPalService.createOrder(amount, currency, description);

            return ResponseEntity.ok(orderResult);
        } catch (Exception e) {
            log.error("Error creating order", e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("error", e.getMessage());
            return ResponseEntity.badRequest().body(errorResponse);
        }
    }

    @GetMapping("/payment-methods/{customerId}")
    public ResponseEntity<List<Map<String, Object>>> getPaymentMethods(@PathVariable String customerId) {
        try {
            log.info("Getting payment methods for customer: {}", customerId);

            // Return demo payment methods for testing
            List<Map<String, Object>> paymentMethods = new ArrayList<>();

            Map<String, Object> method1 = new HashMap<>();
            method1.put("id", 1);
            method1.put("paymentMethodId", "demo_vault_method_123");
            method1.put("paymentType", "Credit Card");
            method1.put("customerId", customerId);
            method1.put("lastFour", "1234");
            method1.put("brand", "VISA");
            method1.put("isDefault", true);
            method1.put("createdAt", "2024-01-01T00:00:00Z");

            Map<String, Object> method2 = new HashMap<>();
            method2.put("id", 2);
            method2.put("paymentMethodId", "demo_vault_method_456");
            method2.put("paymentType", "Credit Card");
            method2.put("customerId", customerId);
            method2.put("lastFour", "5678");
            method2.put("brand", "MC");
            method2.put("isDefault", false);
            method2.put("createdAt", "2024-01-02T00:00:00Z");

            paymentMethods.add(method1);
            paymentMethods.add(method2);

            log.info("Returning {} payment methods for customer: {}", paymentMethods.size(), customerId);
            return ResponseEntity.ok(paymentMethods);
        } catch (Exception e) {
            log.error("Error getting payment methods for customer: {}", customerId, e);
            return ResponseEntity.badRequest().body(new ArrayList<>());
        }
    }

    @PostMapping("/capture-vault-payment")
    public ResponseEntity<Map<String, Object>> captureVaultPayment(@RequestBody Map<String, Object> request) {
        try {
            String paymentMethodId = (String) request.get("paymentMethodId");
            Double amount = (Double) request.get("amount");
            String currency = (String) request.get("currency");
            String description = (String) request.get("description");

            if (paymentMethodId == null || paymentMethodId.isEmpty()) {
                Map<String, Object> errorResponse = new HashMap<>();
                errorResponse.put("error", "Payment Method ID is required for vault payments");
                return ResponseEntity.badRequest().body(errorResponse);
            }

            if (amount == null || amount <= 0) {
                Map<String, Object> errorResponse = new HashMap<>();
                errorResponse.put("error", "Valid amount is required");
                return ResponseEntity.badRequest().body(errorResponse);
            }

            // Set defaults
            if (currency == null || currency.isEmpty()) {
                currency = "USD";
            }
            if (description == null || description.isEmpty()) {
                description = "Vault Payment";
            }

            log.info("Processing vault payment: amount={}, currency={}, paymentMethodId={}", amount, currency, paymentMethodId);

            Map<String, Object> paymentResult = payPalService.createAndCaptureVaultPayment(
                BigDecimal.valueOf(amount), currency, description, paymentMethodId);

            log.info("Vault payment processed successfully");
            return ResponseEntity.ok(paymentResult);
        } catch (Exception e) {
            log.error("Error processing vault payment: {}", e.getMessage(), e);

            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("error", e.getMessage());
            errorResponse.put("errorCode", "VAULT_PAYMENT_ERROR");

            return ResponseEntity.badRequest().body(errorResponse);
        }
    }

    @PostMapping("/capture-order")
    public ResponseEntity<Map<String, Object>> captureOrder(@RequestBody Map<String, String> request) {
        try {
            String orderId = request.get("orderId");

            if (orderId == null || orderId.isEmpty()) {
                Map<String, Object> errorResponse = new HashMap<>();
                errorResponse.put("error", "Order ID is required");
                return ResponseEntity.badRequest().body(errorResponse);
            }

            log.info("Attempting to capture order: {}", orderId);

            Map<String, Object> captureResult = payPalService.captureOrder(orderId);

            log.info("Order captured successfully: {}", orderId);
            return ResponseEntity.ok(captureResult);
        } catch (Exception e) {
            log.error("Error capturing order: {}", e.getMessage(), e);

            Map<String, Object> errorResponse = new HashMap<>();

            // 处理特定的PayPal错误
            if (e.getMessage().contains("ORDER_NOT_APPROVED")) {
                errorResponse.put("error", "Order has not been approved by the user yet. Please ensure the user completes the PayPal payment approval process.");
                errorResponse.put("errorCode", "ORDER_NOT_APPROVED");
            } else if (e.getMessage().contains("not approved")) {
                errorResponse.put("error", "Order is not approved yet. Current status may not allow capture.");
                errorResponse.put("errorCode", "NOT_APPROVED");
            } else {
                errorResponse.put("error", e.getMessage());
            }

            return ResponseEntity.badRequest().body(errorResponse);
        }
    }

    @GetMapping("/order-details/{orderId}")
    public ResponseEntity<Map<String, Object>> getOrderDetails(@PathVariable String orderId) {
        try {
            Map<String, Object> orderDetails = payPalService.getOrderDetails(orderId);

            return ResponseEntity.ok(orderDetails);
        } catch (Exception e) {
            log.error("Error getting order details", e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("error", e.getMessage());
            return ResponseEntity.badRequest().body(errorResponse);
        }
    }

    @PostMapping("/refund")
    public ResponseEntity<Map<String, Object>> processRefund(@RequestBody Map<String, Object> request) {
        try {
            String captureId = request.get("captureId").toString();
            BigDecimal amount = new BigDecimal(request.get("amount").toString());
            String currency = request.getOrDefault("currency", "USD").toString();
            String reason = request.getOrDefault("reason", "Customer requested").toString();

            Refund refund = payPalService.processRefund(captureId, amount, currency, reason);

            Map<String, Object> response = new HashMap<>();
            response.put("refundId", refund.getRefundId());
            response.put("status", refund.getStatus());
            response.put("amount", refund.getAmount());
            response.put("currency", refund.getCurrency());

            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("Error processing refund", e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("error", e.getMessage());
            return ResponseEntity.badRequest().body(errorResponse);
        }
    }

    @PostMapping("/vault-payment-method")
    public ResponseEntity<Map<String, Object>> vaultPaymentMethod(@RequestBody Map<String, Object> request) {
        try {
            String customerId = request.get("customerId").toString();
            String paymentMethodId = request.get("paymentMethodId").toString();
            String paymentType = request.get("paymentType").toString();
            String lastFour = request.get("lastFour").toString();
            String brand = request.get("brand").toString();
            boolean isDefault = Boolean.parseBoolean(request.getOrDefault("isDefault", "false").toString());

            VaultedPaymentMethod paymentMethod = payPalService.vaultPaymentMethod(
                    customerId, paymentMethodId, paymentType, lastFour, brand, isDefault);

            Map<String, Object> response = new HashMap<>();
            response.put("id", paymentMethod.getId());
            response.put("paymentMethodId", paymentMethod.getPaymentMethodId());
            response.put("paymentType", paymentMethod.getPaymentType());
            response.put("lastFour", paymentMethod.getLastFour());
            response.put("brand", paymentMethod.getBrand());
            response.put("isDefault", paymentMethod.isDefault());

            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("Error vaulting payment method", e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("error", e.getMessage());
            return ResponseEntity.badRequest().body(errorResponse);
        }
    }



    @GetMapping("/orders")
    public ResponseEntity<List<Map<String, Object>>> getAllOrders() {
        try {
            List<Order> orders = payPalService.getAllOrders();

            List<Map<String, Object>> response = orders.stream()
                    .map(order -> {
                        Map<String, Object> map = new HashMap<>();
                        map.put("id", order.getId());
                        map.put("paypalOrderId", order.getPaypalOrderId());
                        map.put("amount", order.getAmount());
                        map.put("currency", order.getCurrency());
                        map.put("status", order.getStatus());
                        map.put("createdAt", order.getCreatedAt());

                        // Add refund info if available
                        if (!order.getRefunds().isEmpty()) {
                            map.put("refunded", true);
                            map.put("refundAmount", order.getRefunds().stream()
                                    .map(Refund::getAmount)
                                    .reduce(BigDecimal.ZERO, BigDecimal::add));
                        } else {
                            map.put("refunded", false);
                        }

                        return map;
                    })
                    .collect(Collectors.toList());

            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("Error getting all orders", e);
            return ResponseEntity.badRequest().body(new ArrayList<>());
        }
    }
}
