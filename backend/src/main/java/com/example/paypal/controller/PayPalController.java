package com.example.paypal.controller;

import com.example.paypal.model.Order;
import com.example.paypal.model.Refund;
import com.example.paypal.model.VaultedPaymentMethod;
import com.example.paypal.model.VaultToken;
import com.example.paypal.service.PayPalService;
import com.example.paypal.service.VaultService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/api/paypal")
@RequiredArgsConstructor
@Slf4j
public class PayPalController {

    private final PayPalService payPalService;
    private final VaultService vaultService;

    @PostMapping("/create-order")
    public ResponseEntity<Map<String, Object>> createOrder(@RequestBody Map<String, Object> request) {
        try {
            BigDecimal amount = new BigDecimal(request.get("amount").toString());
            String currency = request.getOrDefault("currency", "USD").toString();
            String description = request.getOrDefault("description", "PayPal Demo Order").toString();

            Map<String, Object> orderResult = payPalService.createOrder(amount, currency, description);

            return ResponseEntity.ok(orderResult);
        } catch (Exception e) {
            log.error("Error creating order", e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("error", e.getMessage());
            return ResponseEntity.badRequest().body(errorResponse);
        }
    }

    @GetMapping("/payment-methods/{customerId}")
    public ResponseEntity<List<Map<String, Object>>> getPaymentMethods(@PathVariable String customerId) {
        try {
            log.info("Getting vault tokens for customer: {}", customerId);

            List<VaultToken> vaultTokens = vaultService.getValidVaultTokens(customerId);

            List<Map<String, Object>> paymentMethods = vaultTokens.stream()
                    .map(token -> {
                        Map<String, Object> method = new HashMap<>();
                        method.put("id", token.getId());
                        method.put("paymentMethodId", token.getVaultToken());
                        method.put("paymentType", token.getPaymentSourceType());
                        method.put("customerId", token.getCustomerId());
                        method.put("lastFour", token.getCardLastFour());
                        method.put("brand", token.getCardBrand());
                        method.put("isDefault", token.isDefault());
                        method.put("createdAt", token.getCreatedAt().toString());
                        method.put("displayName", token.getDisplayName());
                        method.put("isValid", token.isValid());
                        method.put("expiresAt", token.getExpiresAt() != null ? token.getExpiresAt().toString() : null);
                        return method;
                    })
                    .collect(Collectors.toList());

            log.info("Returning {} vault tokens for customer: {}", paymentMethods.size(), customerId);
            return ResponseEntity.ok(paymentMethods);
        } catch (Exception e) {
            log.error("Error getting vault tokens for customer: {}", customerId, e);
            return ResponseEntity.badRequest().body(new ArrayList<>());
        }
    }

    @PostMapping("/capture-vault-payment")
    public ResponseEntity<Map<String, Object>> captureVaultPayment(@RequestBody Map<String, Object> request) {
        try {
            String paymentMethodId = (String) request.get("paymentMethodId");
            Double amount = (Double) request.get("amount");
            String currency = (String) request.get("currency");
            String description = (String) request.get("description");

            if (paymentMethodId == null || paymentMethodId.isEmpty()) {
                Map<String, Object> errorResponse = new HashMap<>();
                errorResponse.put("error", "Payment Method ID is required for vault payments");
                return ResponseEntity.badRequest().body(errorResponse);
            }

            if (amount == null || amount <= 0) {
                Map<String, Object> errorResponse = new HashMap<>();
                errorResponse.put("error", "Valid amount is required");
                return ResponseEntity.badRequest().body(errorResponse);
            }

            // Set defaults
            if (currency == null || currency.isEmpty()) {
                currency = "USD";
            }
            if (description == null || description.isEmpty()) {
                description = "Vault Payment";
            }

            log.info("Processing vault payment: amount={}, currency={}, paymentMethodId={}", amount, currency, paymentMethodId);

            Map<String, Object> paymentResult = payPalService.createAndCaptureVaultPayment(
                BigDecimal.valueOf(amount), currency, description, paymentMethodId);

            log.info("Vault payment processed successfully");
            return ResponseEntity.ok(paymentResult);
        } catch (Exception e) {
            log.error("Error processing vault payment: {}", e.getMessage(), e);

            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("error", e.getMessage());
            errorResponse.put("errorCode", "VAULT_PAYMENT_ERROR");

            return ResponseEntity.badRequest().body(errorResponse);
        }
    }

    @PostMapping("/capture-order")
    public ResponseEntity<Map<String, Object>> captureOrder(@RequestBody Map<String, String> request) {
        try {
            String orderId = request.get("orderId");

            if (orderId == null || orderId.isEmpty()) {
                Map<String, Object> errorResponse = new HashMap<>();
                errorResponse.put("error", "Order ID is required");
                return ResponseEntity.badRequest().body(errorResponse);
            }

            log.info("Attempting to capture order: {}", orderId);

            Map<String, Object> captureResult = payPalService.captureOrder(orderId);

            log.info("Order captured successfully: {}", orderId);
            return ResponseEntity.ok(captureResult);
        } catch (Exception e) {
            log.error("Error capturing order: {}", e.getMessage(), e);

            Map<String, Object> errorResponse = new HashMap<>();

            // 处理特定的PayPal错误
            if (e.getMessage().contains("ORDER_NOT_APPROVED")) {
                errorResponse.put("error", "Order has not been approved by the user yet. Please ensure the user completes the PayPal payment approval process.");
                errorResponse.put("errorCode", "ORDER_NOT_APPROVED");
            } else if (e.getMessage().contains("not approved")) {
                errorResponse.put("error", "Order is not approved yet. Current status may not allow capture.");
                errorResponse.put("errorCode", "NOT_APPROVED");
            } else {
                errorResponse.put("error", e.getMessage());
            }

            return ResponseEntity.badRequest().body(errorResponse);
        }
    }

    @GetMapping("/order-details/{orderId}")
    public ResponseEntity<Map<String, Object>> getOrderDetails(@PathVariable String orderId) {
        try {
            Map<String, Object> orderDetails = payPalService.getOrderDetails(orderId);

            return ResponseEntity.ok(orderDetails);
        } catch (Exception e) {
            log.error("Error getting order details", e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("error", e.getMessage());
            return ResponseEntity.badRequest().body(errorResponse);
        }
    }

    @PostMapping("/refund")
    public ResponseEntity<Map<String, Object>> processRefund(@RequestBody Map<String, Object> request) {
        try {
            String captureId = request.get("captureId").toString();
            BigDecimal amount = new BigDecimal(request.get("amount").toString());
            String currency = request.getOrDefault("currency", "USD").toString();
            String reason = request.getOrDefault("reason", "Customer requested").toString();

            Refund refund = payPalService.processRefund(captureId, amount, currency, reason);

            Map<String, Object> response = new HashMap<>();
            response.put("refundId", refund.getRefundId());
            response.put("status", refund.getStatus());
            response.put("amount", refund.getAmount());
            response.put("currency", refund.getCurrency());

            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("Error processing refund", e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("error", e.getMessage());
            return ResponseEntity.badRequest().body(errorResponse);
        }
    }

    @PostMapping("/vault-payment-method")
    public ResponseEntity<Map<String, Object>> vaultPaymentMethod(@RequestBody Map<String, Object> request) {
        try {
            String customerId = request.get("customerId").toString();
            String paymentMethodId = request.get("paymentMethodId").toString();
            String paymentType = request.get("paymentType").toString();
            String lastFour = request.get("lastFour").toString();
            String brand = request.get("brand").toString();
            boolean isDefault = Boolean.parseBoolean(request.getOrDefault("isDefault", "false").toString());

            VaultedPaymentMethod paymentMethod = payPalService.vaultPaymentMethod(
                    customerId, paymentMethodId, paymentType, lastFour, brand, isDefault);

            Map<String, Object> response = new HashMap<>();
            response.put("id", paymentMethod.getId());
            response.put("paymentMethodId", paymentMethod.getPaymentMethodId());
            response.put("paymentType", paymentMethod.getPaymentType());
            response.put("lastFour", paymentMethod.getLastFour());
            response.put("brand", paymentMethod.getBrand());
            response.put("isDefault", paymentMethod.isDefault());

            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("Error vaulting payment method", e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("error", e.getMessage());
            return ResponseEntity.badRequest().body(errorResponse);
        }
    }

    // Vault Management Endpoints

    @PostMapping("/vault/setup")
    public ResponseEntity<Map<String, Object>> createVaultSetup(@RequestBody Map<String, Object> request) {
        try {
            String customerId = request.get("customerId").toString();
            String returnUrl = request.getOrDefault("returnUrl", "http://localhost:3000/vault/complete").toString();
            String cancelUrl = request.getOrDefault("cancelUrl", "http://localhost:3000/vault").toString();

            log.info("Creating vault setup for customer: {}", customerId);

            Map<String, Object> setupResult = vaultService.createVaultSetupToken(customerId, returnUrl, cancelUrl);

            return ResponseEntity.ok(setupResult);
        } catch (Exception e) {
            log.error("Error creating vault setup", e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("error", e.getMessage());
            return ResponseEntity.badRequest().body(errorResponse);
        }
    }

    @PostMapping("/vault/complete")
    public ResponseEntity<Map<String, Object>> completeVaultSetup(@RequestBody Map<String, Object> request) {
        try {
            String setupToken = request.get("setupToken").toString();
            String customerId = request.get("customerId").toString();
            boolean isDefault = Boolean.parseBoolean(request.getOrDefault("isDefault", "false").toString());

            log.info("Completing vault setup for token: {} and customer: {}", setupToken, customerId);

            VaultToken vaultToken = vaultService.completeVaultSetup(setupToken, customerId, isDefault);

            Map<String, Object> response = new HashMap<>();
            response.put("id", vaultToken.getId());
            response.put("vaultToken", vaultToken.getVaultToken());
            response.put("paymentType", vaultToken.getPaymentSourceType());
            response.put("displayName", vaultToken.getDisplayName());
            response.put("isDefault", vaultToken.isDefault());
            response.put("createdAt", vaultToken.getCreatedAt().toString());

            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("Error completing vault setup", e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("error", e.getMessage());
            return ResponseEntity.badRequest().body(errorResponse);
        }
    }

    @PostMapping("/vault/set-default")
    public ResponseEntity<Map<String, Object>> setDefaultVaultToken(@RequestBody Map<String, Object> request) {
        try {
            String customerId = request.get("customerId").toString();
            Long vaultTokenId = Long.parseLong(request.get("vaultTokenId").toString());

            log.info("Setting default vault token: {} for customer: {}", vaultTokenId, customerId);

            VaultToken vaultToken = vaultService.setDefaultVaultToken(customerId, vaultTokenId);

            Map<String, Object> response = new HashMap<>();
            response.put("id", vaultToken.getId());
            response.put("isDefault", vaultToken.isDefault());
            response.put("message", "Default payment method updated successfully");

            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("Error setting default vault token", e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("error", e.getMessage());
            return ResponseEntity.badRequest().body(errorResponse);
        }
    }

    @DeleteMapping("/vault/{customerId}/{vaultTokenId}")
    public ResponseEntity<Map<String, Object>> deleteVaultToken(@PathVariable String customerId, @PathVariable Long vaultTokenId) {
        try {
            log.info("Deleting vault token: {} for customer: {}", vaultTokenId, customerId);

            vaultService.deleteVaultToken(customerId, vaultTokenId);

            Map<String, Object> response = new HashMap<>();
            response.put("message", "Payment method deleted successfully");

            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("Error deleting vault token", e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("error", e.getMessage());
            return ResponseEntity.badRequest().body(errorResponse);
        }
    }

    @GetMapping("/vault/stats/{customerId}")
    public ResponseEntity<Map<String, Object>> getVaultStats(@PathVariable String customerId) {
        try {
            Map<String, Object> stats = vaultService.getVaultTokenStats(customerId);
            return ResponseEntity.ok(stats);
        } catch (Exception e) {
            log.error("Error getting vault stats", e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("error", e.getMessage());
            return ResponseEntity.badRequest().body(errorResponse);
        }
    }

    @PostMapping("/vault/cleanup")
    public ResponseEntity<Map<String, Object>> cleanupExpiredTokens() {
        try {
            log.info("Cleaning up expired vault tokens");

            int cleanedCount = vaultService.cleanupExpiredTokens();

            Map<String, Object> response = new HashMap<>();
            response.put("cleanedCount", cleanedCount);
            response.put("message", "Expired tokens cleaned up successfully");

            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("Error cleaning up expired tokens", e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("error", e.getMessage());
            return ResponseEntity.badRequest().body(errorResponse);
        }
    }



    @GetMapping("/orders")
    public ResponseEntity<List<Map<String, Object>>> getAllOrders() {
        try {
            List<Order> orders = payPalService.getAllOrders();

            List<Map<String, Object>> response = orders.stream()
                    .map(order -> {
                        Map<String, Object> map = new HashMap<>();
                        map.put("id", order.getId());
                        map.put("paypalOrderId", order.getPaypalOrderId());
                        map.put("amount", order.getAmount());
                        map.put("currency", order.getCurrency());
                        map.put("status", order.getStatus());
                        map.put("createdAt", order.getCreatedAt());

                        // Add refund info if available
                        if (!order.getRefunds().isEmpty()) {
                            map.put("refunded", true);
                            map.put("refundAmount", order.getRefunds().stream()
                                    .map(Refund::getAmount)
                                    .reduce(BigDecimal.ZERO, BigDecimal::add));
                        } else {
                            map.put("refunded", false);
                        }

                        return map;
                    })
                    .collect(Collectors.toList());

            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("Error getting all orders", e);
            return ResponseEntity.badRequest().body(new ArrayList<>());
        }
    }
}
