package com.example.paypal.service;

import com.example.paypal.model.Order;
import com.example.paypal.model.Refund;
import com.example.paypal.model.VaultedPaymentMethod;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

public interface PayPalService {

    /**
     * Creates a PayPal order
     *
     * @param amount The order amount
     * @param currency The currency code
     * @param description The order description
     * @return Map containing order details including the order ID
     */
    Map<String, Object> createOrder(BigDecimal amount, String currency, String description);

    /**
     * Captures a PayPal order payment
     *
     * @param orderId The PayPal order ID
     * @return Map containing capture details
     */
    Map<String, Object> captureOrder(String orderId);

    /**
     * Captures a PayPal order payment using vault payment method
     *
     * @param orderId The PayPal order ID
     * @param paymentMethodId The vault payment method ID
     * @return Map containing capture details
     */
    Map<String, Object> captureOrder(String orderId, String paymentMethodId);

    /**
     * Creates and captures a vault payment in one step
     *
     * @param amount Payment amount
     * @param currency Payment currency
     * @param description Payment description
     * @param paymentMethodId The vault payment method ID
     * @return Map containing payment details
     */
    Map<String, Object> createAndCaptureVaultPayment(BigDecimal amount, String currency, String description, String paymentMethodId);

    /**
     * Gets details of a PayPal order
     *
     * @param orderId The PayPal order ID
     * @return Map containing order details
     */
    Map<String, Object> getOrderDetails(String orderId);

    /**
     * Processes a refund for a captured payment
     *
     * @param captureId The capture ID
     * @param amount The refund amount
     * @param currency The currency code
     * @param reason The reason for the refund
     * @return The created Refund entity
     */
    Refund processRefund(String captureId, BigDecimal amount, String currency, String reason);

    /**
     * Stores a payment method in the vault
     *
     * @param customerId The customer ID
     * @param paymentMethodId The payment method ID
     * @param paymentType The payment method type
     * @param lastFour The last four digits
     * @param brand The card brand
     * @param isDefault Whether this is the default payment method
     * @return The created VaultedPaymentMethod entity
     */
    VaultedPaymentMethod vaultPaymentMethod(String customerId, String paymentMethodId,
                                           String paymentType, String lastFour,
                                           String brand, boolean isDefault);

    /**
     * Gets all vaulted payment methods for a customer
     *
     * @param customerId The customer ID
     * @return List of vaulted payment methods
     */
    List<VaultedPaymentMethod> getVaultedPaymentMethods(String customerId);

    /**
     * Gets all orders
     *
     * @return List of orders
     */
    List<Order> getAllOrders();

    /**
     * Gets an order by its PayPal order ID
     *
     * @param paypalOrderId The PayPal order ID
     * @return The Order entity
     */
    Order getOrderByPayPalOrderId(String paypalOrderId);
}
