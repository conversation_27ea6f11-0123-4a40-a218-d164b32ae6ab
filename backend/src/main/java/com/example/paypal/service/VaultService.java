package com.example.paypal.service;

import com.example.paypal.model.VaultToken;

import java.util.List;
import java.util.Map;

public interface VaultService {
    
    /**
     * Create vault setup token for payment method setup
     * 
     * @param customerId Customer ID
     * @param returnUrl Return URL after setup
     * @param cancelUrl Cancel URL
     * @return Setup token and approval URL
     */
    Map<String, Object> createVaultSetupToken(String customerId, String returnUrl, String cancelUrl);
    
    /**
     * Complete vault setup and save token
     * 
     * @param setupToken Setup token from PayPal
     * @param customerId Customer ID
     * @param isDefault Whether this should be the default payment method
     * @return Saved vault token
     */
    VaultToken completeVaultSetup(String setupToken, String customerId, boolean isDefault);
    
    /**
     * Get all valid vault tokens for a customer
     * 
     * @param customerId Customer ID
     * @return List of valid vault tokens
     */
    List<VaultToken> getValidVaultTokens(String customerId);
    
    /**
     * Get a specific vault token
     * 
     * @param vaultToken Vault token string
     * @return Vault token entity
     */
    VaultToken getVaultToken(String vaultToken);
    
    /**
     * Set a vault token as default
     * 
     * @param customerId Customer ID
     * @param vaultTokenId Vault token ID
     * @return Updated vault token
     */
    VaultToken setDefaultVaultToken(String customerId, Long vaultTokenId);
    
    /**
     * Delete a vault token
     * 
     * @param customerId Customer ID
     * @param vaultTokenId Vault token ID
     */
    void deleteVaultToken(String customerId, Long vaultTokenId);
    
    /**
     * Create payment using vault token
     * 
     * @param vaultToken Vault token
     * @param amount Payment amount
     * @param currency Payment currency
     * @param description Payment description
     * @return Payment result
     */
    Map<String, Object> createVaultPayment(String vaultToken, String amount, String currency, String description);
    
    /**
     * Validate vault token
     * 
     * @param vaultToken Vault token string
     * @return True if valid
     */
    boolean isVaultTokenValid(String vaultToken);
    
    /**
     * Clean up expired tokens
     * 
     * @return Number of tokens cleaned up
     */
    int cleanupExpiredTokens();
    
    /**
     * Get vault token statistics for a customer
     * 
     * @param customerId Customer ID
     * @return Statistics map
     */
    Map<String, Object> getVaultTokenStats(String customerId);
}
