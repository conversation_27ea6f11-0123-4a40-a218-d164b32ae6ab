package com.example.paypal.service;

import com.example.paypal.model.VaultToken;
import com.example.paypal.repository.VaultTokenRepository;
import com.paypal.core.PayPalHttpClient;
import com.paypal.http.HttpResponse;
import com.paypal.orders.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
@RequiredArgsConstructor
@Slf4j
public class VaultServiceImpl implements VaultService {
    
    private final VaultTokenRepository vaultTokenRepository;
    private final PayPalHttpClient payPalHttpClient;
    
    @Override
    public Map<String, Object> createVaultSetupToken(String customerId, String returnUrl, String cancelUrl) {
        try {
            log.info("Creating vault setup token for customer: {}", customerId);
            
            // Create setup token request
            OrderRequest orderRequest = new OrderRequest();
            orderRequest.checkoutPaymentIntent("CAPTURE");
            
            // Set application context for vault setup
            ApplicationContext applicationContext = new ApplicationContext()
                    .returnUrl(returnUrl)
                    .cancelUrl(cancelUrl)
                    .userAction("PAY_NOW");
            
            orderRequest.applicationContext(applicationContext);
            
            // Create purchase unit with zero amount for setup
            AmountWithBreakdown amountBreakdown = new AmountWithBreakdown()
                    .currencyCode("USD")
                    .value("0.00");
            
            PurchaseUnitRequest purchaseUnitRequest = new PurchaseUnitRequest()
                    .amountWithBreakdown(amountBreakdown)
                    .description("Payment method setup");
            
            orderRequest.purchaseUnits(List.of(purchaseUnitRequest));
            
            // Add payment source for vault
            PaymentSource paymentSource = new PaymentSource();
            Card card = new Card();
            CardAttributes cardAttributes = new CardAttributes();
            
            // Set vault attributes
            Vault vault = new Vault()
                    .storeInVault("ON_SUCCESS")
                    .usageType("MERCHANT");
            
            cardAttributes.vault(vault);
            card.attributes(cardAttributes);
            paymentSource.card(card);
            orderRequest.paymentSource(paymentSource);
            
            OrdersCreateRequest request = new OrdersCreateRequest();
            request.prefer("return=representation");
            request.requestBody(orderRequest);
            
            HttpResponse<Order> response = payPalHttpClient.execute(request);
            Order order = response.result();
            
            log.info("Created vault setup order: {}", order.id());
            
            // Find approval URL
            String approvalUrl = null;
            if (order.links() != null) {
                for (LinkDescription link : order.links()) {
                    if ("approve".equals(link.rel())) {
                        approvalUrl = link.href();
                        break;
                    }
                }
            }
            
            Map<String, Object> result = new HashMap<>();
            result.put("setupToken", order.id());
            result.put("approvalUrl", approvalUrl);
            result.put("customerId", customerId);
            
            return result;
            
        } catch (IOException e) {
            log.error("Error creating vault setup token", e);
            throw new RuntimeException("Failed to create vault setup token: " + e.getMessage(), e);
        }
    }
    
    @Override
    @Transactional
    public VaultToken completeVaultSetup(String setupToken, String customerId, boolean isDefault) {
        try {
            log.info("Completing vault setup for token: {} and customer: {}", setupToken, customerId);
            
            // Get order details to extract vault information
            OrdersGetRequest getRequest = new OrdersGetRequest(setupToken);
            HttpResponse<Order> getResponse = payPalHttpClient.execute(getRequest);
            Order order = getResponse.result();
            
            if (!"APPROVED".equals(order.status())) {
                throw new RuntimeException("Setup token not approved: " + order.status());
            }
            
            // Capture the setup order to complete vault setup
            OrdersCaptureRequest captureRequest = new OrdersCaptureRequest(setupToken);
            captureRequest.prefer("return=representation");
            
            HttpResponse<Order> captureResponse = payPalHttpClient.execute(captureRequest);
            Order capturedOrder = captureResponse.result();
            
            // Extract vault token from payment source
            String vaultToken = extractVaultToken(capturedOrder);
            if (vaultToken == null) {
                throw new RuntimeException("Failed to extract vault token from order");
            }
            
            // Extract payment method details
            Map<String, String> paymentDetails = extractPaymentDetails(capturedOrder);
            
            // Clear default if this should be the new default
            if (isDefault) {
                vaultTokenRepository.clearDefaultForCustomer(customerId, LocalDateTime.now());
            }
            
            // Save vault token
            VaultToken vaultTokenEntity = VaultToken.builder()
                    .customerId(customerId)
                    .vaultToken(vaultToken)
                    .paymentSourceType(paymentDetails.get("type"))
                    .cardBrand(paymentDetails.get("brand"))
                    .cardLastFour(paymentDetails.get("lastFour"))
                    .cardExpiryMonth(paymentDetails.get("expiryMonth"))
                    .cardExpiryYear(paymentDetails.get("expiryYear"))
                    .paypalEmail(paymentDetails.get("email"))
                    .isDefault(isDefault)
                    .isActive(true)
                    .build();
            
            VaultToken savedToken = vaultTokenRepository.save(vaultTokenEntity);
            
            log.info("Vault token saved successfully: {}", savedToken.getId());
            return savedToken;
            
        } catch (IOException e) {
            log.error("Error completing vault setup", e);
            throw new RuntimeException("Failed to complete vault setup: " + e.getMessage(), e);
        }
    }
    
    @Override
    public List<VaultToken> getValidVaultTokens(String customerId) {
        return vaultTokenRepository.findValidTokensByCustomerId(customerId, LocalDateTime.now());
    }
    
    @Override
    public VaultToken getVaultToken(String vaultToken) {
        return vaultTokenRepository.findByVaultTokenAndIsActiveTrue(vaultToken)
                .orElseThrow(() -> new RuntimeException("Vault token not found or inactive: " + vaultToken));
    }
    
    @Override
    @Transactional
    public VaultToken setDefaultVaultToken(String customerId, Long vaultTokenId) {
        // Clear current default
        vaultTokenRepository.clearDefaultForCustomer(customerId, LocalDateTime.now());
        
        // Set new default
        VaultToken vaultToken = vaultTokenRepository.findById(vaultTokenId)
                .orElseThrow(() -> new RuntimeException("Vault token not found: " + vaultTokenId));
        
        if (!customerId.equals(vaultToken.getCustomerId())) {
            throw new RuntimeException("Vault token does not belong to customer");
        }
        
        vaultToken.setDefault(true);
        vaultToken.setUpdatedAt(LocalDateTime.now());
        
        return vaultTokenRepository.save(vaultToken);
    }
    
    @Override
    @Transactional
    public void deleteVaultToken(String customerId, Long vaultTokenId) {
        VaultToken vaultToken = vaultTokenRepository.findById(vaultTokenId)
                .orElseThrow(() -> new RuntimeException("Vault token not found: " + vaultTokenId));
        
        if (!customerId.equals(vaultToken.getCustomerId())) {
            throw new RuntimeException("Vault token does not belong to customer");
        }
        
        vaultToken.setActive(false);
        vaultToken.setUpdatedAt(LocalDateTime.now());
        vaultTokenRepository.save(vaultToken);
        
        log.info("Vault token deactivated: {}", vaultTokenId);
    }
    
    @Override
    public Map<String, Object> createVaultPayment(String vaultToken, String amount, String currency, String description) {
        try {
            log.info("Creating vault payment with token: {}", vaultToken);
            
            // Validate vault token
            VaultToken token = getVaultToken(vaultToken);
            if (!token.isValid()) {
                throw new RuntimeException("Vault token is invalid or expired");
            }
            
            // Create order with vault payment source
            OrderRequest orderRequest = buildVaultPaymentOrder(vaultToken, amount, currency, description);
            
            OrdersCreateRequest createRequest = new OrdersCreateRequest();
            createRequest.prefer("return=representation");
            createRequest.requestBody(orderRequest);
            
            HttpResponse<Order> createResponse = payPalHttpClient.execute(createRequest);
            Order order = createResponse.result();
            
            log.info("Created vault payment order: {}", order.id());
            
            // Capture immediately since vault payments are pre-approved
            OrdersCaptureRequest captureRequest = new OrdersCaptureRequest(order.id());
            captureRequest.prefer("return=representation");
            
            HttpResponse<Order> captureResponse = payPalHttpClient.execute(captureRequest);
            Order capturedOrder = captureResponse.result();
            
            // Mark token as used
            token.markAsUsed();
            vaultTokenRepository.save(token);
            
            log.info("Vault payment captured successfully: {}", capturedOrder.id());
            
            Map<String, Object> result = new HashMap<>();
            result.put("id", capturedOrder.id());
            result.put("status", capturedOrder.status());
            result.put("purchase_units", capturedOrder.purchaseUnits());
            result.put("payment_method", "vault");
            result.put("vault_token", vaultToken);
            
            return result;
            
        } catch (IOException e) {
            log.error("Error creating vault payment", e);
            throw new RuntimeException("Failed to create vault payment: " + e.getMessage(), e);
        }
    }
    
    @Override
    public boolean isVaultTokenValid(String vaultToken) {
        try {
            VaultToken token = vaultTokenRepository.findByVaultTokenAndIsActiveTrue(vaultToken)
                    .orElse(null);
            return token != null && token.isValid();
        } catch (Exception e) {
            log.error("Error validating vault token", e);
            return false;
        }
    }
    
    @Override
    @Transactional
    public int cleanupExpiredTokens() {
        log.info("Cleaning up expired vault tokens");
        int count = vaultTokenRepository.deactivateExpiredTokens(LocalDateTime.now());
        log.info("Deactivated {} expired vault tokens", count);
        return count;
    }
    
    @Override
    public Map<String, Object> getVaultTokenStats(String customerId) {
        List<VaultToken> validTokens = getValidVaultTokens(customerId);
        long totalTokens = vaultTokenRepository.countByCustomerIdAndIsActiveTrue(customerId);
        
        Map<String, Object> stats = new HashMap<>();
        stats.put("totalTokens", totalTokens);
        stats.put("validTokens", validTokens.size());
        stats.put("hasDefault", validTokens.stream().anyMatch(VaultToken::isDefault));
        
        return stats;
    }
    
    /**
     * Extract vault token from captured order
     */
    private String extractVaultToken(Order order) {
        // This is a simplified extraction - in real implementation,
        // you would extract the actual vault token from the order response
        // The exact path depends on PayPal's response structure
        
        if (order.purchaseUnits() != null && !order.purchaseUnits().isEmpty()) {
            PurchaseUnit purchaseUnit = order.purchaseUnits().get(0);
            if (purchaseUnit.payments() != null && 
                purchaseUnit.payments().captures() != null && 
                !purchaseUnit.payments().captures().isEmpty()) {
                
                // In a real implementation, extract from payment source vault info
                return "vault_" + order.id() + "_" + System.currentTimeMillis();
            }
        }
        
        return null;
    }
    
    /**
     * Extract payment method details from order
     */
    private Map<String, String> extractPaymentDetails(Order order) {
        Map<String, String> details = new HashMap<>();
        
        // This is simplified - extract real details from PayPal response
        details.put("type", "CARD");
        details.put("brand", "VISA");
        details.put("lastFour", "1234");
        details.put("expiryMonth", "12");
        details.put("expiryYear", "2025");
        
        return details;
    }
    
    /**
     * Build order request for vault payment
     */
    private OrderRequest buildVaultPaymentOrder(String vaultToken, String amount, String currency, String description) {
        OrderRequest orderRequest = new OrderRequest();
        orderRequest.checkoutPaymentIntent("CAPTURE");
        
        // Create amount
        AmountWithBreakdown amountBreakdown = new AmountWithBreakdown()
                .currencyCode(currency)
                .value(amount);
        
        // Create purchase unit
        PurchaseUnitRequest purchaseUnitRequest = new PurchaseUnitRequest()
                .amountWithBreakdown(amountBreakdown)
                .description(description);
        
        orderRequest.purchaseUnits(List.of(purchaseUnitRequest));
        
        // Add vault payment source
        PaymentSource paymentSource = new PaymentSource();
        Card card = new Card();
        CardAttributes cardAttributes = new CardAttributes();
        
        // Set vault token
        Vault vault = new Vault()
                .id(vaultToken);
        
        cardAttributes.vault(vault);
        card.attributes(cardAttributes);
        paymentSource.card(card);
        orderRequest.paymentSource(paymentSource);
        
        return orderRequest;
    }
}
