package com.example.paypal.service;

import com.example.paypal.model.Order;
import com.example.paypal.model.PaymentTransaction;
import com.example.paypal.model.Refund;
import com.example.paypal.model.VaultedPaymentMethod;
import com.example.paypal.repository.OrderRepository;
import com.example.paypal.repository.PaymentTransactionRepository;
import com.example.paypal.repository.RefundRepository;
import com.example.paypal.repository.VaultedPaymentMethodRepository;
import com.paypal.core.PayPalHttpClient;
import com.paypal.http.HttpResponse;
import com.paypal.orders.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;

@Service
@RequiredArgsConstructor
@Slf4j
public class PayPalServiceImpl implements PayPalService {

    private final PayPalHttpClient payPalHttpClient;
    private final OrderRepository orderRepository;
    private final PaymentTransactionRepository paymentTransactionRepository;
    private final RefundRepository refundRepository;
    private final VaultedPaymentMethodRepository vaultedPaymentMethodRepository;

    @Override
    @Transactional
    public Map<String, Object> createOrder(BigDecimal amount, String currency, String description) {
        OrdersCreateRequest request = new OrdersCreateRequest();
        request.prefer("return=representation");
        request.requestBody(buildOrderRequest(amount, currency, description));

        try {
            HttpResponse<com.paypal.orders.Order> response = payPalHttpClient.execute(request);
            com.paypal.orders.Order paypalOrder = response.result();

            // Save order to database
            Order order = Order.builder()
                    .paypalOrderId(paypalOrder.id())
                    .amount(amount)
                    .currency(currency)
                    .status(paypalOrder.status())
                    .build();

            orderRepository.save(order);

            Map<String, Object> result = new HashMap<>();
            result.put("id", paypalOrder.id());
            result.put("status", paypalOrder.status());
            result.put("links", paypalOrder.links());

            return result;
        } catch (IOException e) {
            log.error("Error creating PayPal order", e);
            throw new RuntimeException("Failed to create PayPal order", e);
        }
    }

    @Override
    @Transactional
    public Map<String, Object> captureOrder(String orderId) {
        try {
            // First, get the order details to check its status
            Map<String, Object> orderDetails = getOrderDetails(orderId);
            String orderStatus = (String) orderDetails.get("status");

            log.info("Order {} current status: {}", orderId, orderStatus);

            // Check if order is in the correct status for capture
            if (!"APPROVED".equals(orderStatus)) {
                throw new RuntimeException("Order is not approved yet. Current status: " + orderStatus +
                    ". Please ensure the user has completed the PayPal approval process.");
            }

            OrdersCaptureRequest request = new OrdersCaptureRequest(orderId);
            request.prefer("return=representation");

            HttpResponse<com.paypal.orders.Order> response = payPalHttpClient.execute(request);
            com.paypal.orders.Order capturedOrder = response.result();

            log.info("Order {} captured successfully with status: {}", orderId, capturedOrder.status());

            // Update order in database
            Order order = orderRepository.findByPaypalOrderId(orderId)
                    .orElseThrow(() -> new RuntimeException("Order not found: " + orderId));

            order.setStatus(capturedOrder.status());
            orderRepository.save(order);

            // Save payment transaction
            if (capturedOrder.purchaseUnits().size() > 0 &&
                capturedOrder.purchaseUnits().get(0).payments() != null &&
                capturedOrder.purchaseUnits().get(0).payments().captures() != null &&
                !capturedOrder.purchaseUnits().get(0).payments().captures().isEmpty()) {

                Capture capture = capturedOrder.purchaseUnits().get(0).payments().captures().get(0);

                PaymentTransaction transaction = PaymentTransaction.builder()
                        .transactionId(capture.id())
                        .amount(new BigDecimal(capture.amount().value()))
                        .currency(capture.amount().currencyCode())
                        .status(capture.status())
                        .paymentMethod("PAYPAL")
                        .order(order)
                        .build();

                paymentTransactionRepository.save(transaction);

                log.info("Payment transaction saved: {}", capture.id());
            }

            Map<String, Object> result = new HashMap<>();
            result.put("id", capturedOrder.id());
            result.put("status", capturedOrder.status());
            result.put("purchase_units", capturedOrder.purchaseUnits());

            return result;
        } catch (IOException e) {
            log.error("Error capturing PayPal order: {}", e.getMessage(), e);

            // Check if it's the ORDER_NOT_APPROVED error
            if (e.getMessage().contains("ORDER_NOT_APPROVED")) {
                throw new RuntimeException("Order has not been approved by the user yet. " +
                    "Please ensure the user completes the PayPal payment approval process before capturing.");
            }

            throw new RuntimeException("Failed to capture PayPal order: " + e.getMessage(), e);
        }
    }

    @Override
    @Transactional
    public Map<String, Object> captureOrder(String orderId, String paymentMethodId) {
        try {
            // First, get the order details to check its status
            Map<String, Object> orderDetails = getOrderDetails(orderId);
            String orderStatus = (String) orderDetails.get("status");

            log.info("Order {} current status: {}", orderId, orderStatus);

            // For vault payments, we need to handle CREATED status differently
            boolean isVaultPayment = paymentMethodId != null && !paymentMethodId.isEmpty();

            if (isVaultPayment) {
                log.info("Processing vault payment for order: {} with payment method: {}", orderId, paymentMethodId);
                // For vault payments, we can capture orders in CREATED status
                if (!"CREATED".equals(orderStatus) && !"APPROVED".equals(orderStatus)) {
                    throw new RuntimeException("Order status not suitable for vault payment capture. Current status: " + orderStatus);
                }

                // For vault payments, we need to add payment source to the capture request
                log.info("Using vault payment method for capture: {}", paymentMethodId);
            } else {
                // For regular payments, order must be APPROVED
                if (!"APPROVED".equals(orderStatus)) {
                    throw new RuntimeException("Order is not approved yet. Current status: " + orderStatus +
                        ". Please ensure the user has completed the PayPal approval process.");
                }
            }

            OrdersCaptureRequest request = new OrdersCaptureRequest(orderId);
            request.prefer("return=representation");

            HttpResponse<com.paypal.orders.Order> response = payPalHttpClient.execute(request);
            com.paypal.orders.Order capturedOrder = response.result();

            log.info("Order {} captured successfully with status: {}", orderId, capturedOrder.status());

            // Update order in database
            Order order = orderRepository.findByPaypalOrderId(orderId)
                    .orElseThrow(() -> new RuntimeException("Order not found: " + orderId));

            order.setStatus(capturedOrder.status());
            orderRepository.save(order);

            // Save payment transaction
            if (capturedOrder.purchaseUnits().size() > 0 &&
                capturedOrder.purchaseUnits().get(0).payments() != null &&
                capturedOrder.purchaseUnits().get(0).payments().captures() != null &&
                !capturedOrder.purchaseUnits().get(0).payments().captures().isEmpty()) {

                Capture capture = capturedOrder.purchaseUnits().get(0).payments().captures().get(0);

                PaymentTransaction transaction = PaymentTransaction.builder()
                        .transactionId(capture.id())
                        .amount(new BigDecimal(capture.amount().value()))
                        .currency(capture.amount().currencyCode())
                        .status(capture.status())
                        .paymentMethod(isVaultPayment ? "PAYPAL_VAULT" : "PAYPAL")
                        .order(order)
                        .build();

                paymentTransactionRepository.save(transaction);

                log.info("Payment transaction saved: {}", capture.id());
            }

            Map<String, Object> result = new HashMap<>();
            result.put("id", capturedOrder.id());
            result.put("status", capturedOrder.status());
            result.put("purchase_units", capturedOrder.purchaseUnits());

            return result;
        } catch (IOException e) {
            log.error("Error capturing PayPal order: {}", e.getMessage(), e);

            // Check if it's the ORDER_NOT_APPROVED error
            if (e.getMessage().contains("ORDER_NOT_APPROVED")) {
                throw new RuntimeException("Order has not been approved by the user yet. " +
                    "Please ensure the user completes the PayPal payment approval process before capturing.");
            }

            throw new RuntimeException("Failed to capture PayPal order: " + e.getMessage(), e);
        }
    }

    @Override
    public Map<String, Object> createAndCaptureVaultPayment(BigDecimal amount, String currency, String description, String paymentMethodId) {
        try {
            log.info("Creating and capturing vault payment: amount={}, currency={}, paymentMethodId={}", amount, currency, paymentMethodId);

            // Check if we have a real vault token or if this is a demo
            if (paymentMethodId.startsWith("test_") || paymentMethodId.startsWith("demo_")) {
                log.info("Processing demo vault payment");
                return processDemoVaultPayment(amount, currency, description, paymentMethodId);
            } else {
                log.info("Processing real vault payment");
                return processRealVaultPayment(amount, currency, description, paymentMethodId);
            }

        } catch (Exception e) {
            log.error("Error creating and capturing vault payment", e);
            throw new RuntimeException("Failed to process vault payment: " + e.getMessage(), e);
        }
    }

    /**
     * Process real vault payment using PayPal API
     */
    private Map<String, Object> processRealVaultPayment(BigDecimal amount, String currency, String description, String paymentMethodId) {
        try {
            // Step 1: Create order with vault payment source
            Map<String, Object> orderData = createOrderWithVaultPaymentSource(amount, currency, description, paymentMethodId);
            String orderId = (String) orderData.get("id");

            log.info("Created vault order: {}, now capturing...", orderId);

            // Step 2: Capture the order immediately (vault orders are auto-approved)
            return captureVaultOrder(orderId, paymentMethodId);

        } catch (Exception e) {
            log.error("Error processing real vault payment", e);
            throw new RuntimeException("Failed to process real vault payment: " + e.getMessage(), e);
        }
    }

    /**
     * Process demo vault payment (for testing purposes)
     */
    private Map<String, Object> processDemoVaultPayment(BigDecimal amount, String currency, String description, String paymentMethodId) {
        try {
            log.info("Processing demo vault payment - this simulates a successful vault payment");

            // Create a database order record
            Order order = Order.builder()
                    .paypalOrderId("DEMO_VAULT_" + System.currentTimeMillis())
                    .amount(amount)
                    .currency(currency)
                    .status("COMPLETED")
                    .build();

            Order savedOrder = orderRepository.save(order);
            log.info("Created demo vault payment order: {}", savedOrder.getPaypalOrderId());

            // Create a payment transaction record
            PaymentTransaction transaction = PaymentTransaction.builder()
                    .transactionId("DEMO_VAULT_TXN_" + System.currentTimeMillis())
                    .amount(amount)
                    .currency(currency)
                    .status("COMPLETED")
                    .paymentMethod("PAYPAL_VAULT_DEMO")
                    .order(savedOrder)
                    .build();

            paymentTransactionRepository.save(transaction);
            log.info("Demo vault payment transaction saved: {}", transaction.getTransactionId());

            // Return response in PayPal format
            Map<String, Object> result = new HashMap<>();
            result.put("id", savedOrder.getPaypalOrderId());
            result.put("status", "COMPLETED");
            result.put("payment_method", "vault");
            result.put("payment_method_id", paymentMethodId);
            result.put("demo", true);

            // Add purchase units for compatibility
            Map<String, Object> purchaseUnit = new HashMap<>();
            Map<String, Object> amountData = new HashMap<>();
            amountData.put("value", amount.toString());
            amountData.put("currency_code", currency);
            purchaseUnit.put("amount", amountData);

            // Add capture info
            Map<String, Object> payments = new HashMap<>();
            Map<String, Object> capture = new HashMap<>();
            capture.put("id", transaction.getTransactionId());
            capture.put("status", "COMPLETED");
            capture.put("amount", amountData);
            payments.put("captures", List.of(capture));
            purchaseUnit.put("payments", payments);

            result.put("purchase_units", List.of(purchaseUnit));

            log.info("Demo vault payment completed successfully: {}", savedOrder.getPaypalOrderId());
            return result;

        } catch (Exception e) {
            log.error("Error processing demo vault payment", e);
            throw new RuntimeException("Failed to process demo vault payment: " + e.getMessage(), e);
        }
    }

    /**
     * Create order with vault payment source
     */
    private Map<String, Object> createOrderWithVaultPaymentSource(BigDecimal amount, String currency, String description, String paymentMethodId) {
        try {
            // Build order request with vault payment source
            OrderRequest orderRequest = buildVaultOrderRequest(amount, currency, description, paymentMethodId);

            OrdersCreateRequest request = new OrdersCreateRequest();
            request.prefer("return=representation");
            request.requestBody(orderRequest);

            HttpResponse<com.paypal.orders.Order> response = payPalHttpClient.execute(request);
            com.paypal.orders.Order paypalOrder = response.result();

            // Save order to database
            Order order = Order.builder()
                    .paypalOrderId(paypalOrder.id())
                    .amount(amount)
                    .currency(currency)
                    .status(paypalOrder.status())
                    .build();

            orderRepository.save(order);

            log.info("Created vault order with PayPal: {} (status: {})", paypalOrder.id(), paypalOrder.status());

            Map<String, Object> result = new HashMap<>();
            result.put("id", paypalOrder.id());
            result.put("status", paypalOrder.status());
            result.put("links", paypalOrder.links());

            return result;
        } catch (IOException e) {
            log.error("Error creating vault order with PayPal", e);
            throw new RuntimeException("Failed to create vault order with PayPal", e);
        }
    }

    /**
     * Capture vault order
     */
    private Map<String, Object> captureVaultOrder(String orderId, String paymentMethodId) {
        try {
            OrdersCaptureRequest request = new OrdersCaptureRequest(orderId);
            request.prefer("return=representation");

            HttpResponse<com.paypal.orders.Order> response = payPalHttpClient.execute(request);
            com.paypal.orders.Order capturedOrder = response.result();

            log.info("Vault order {} captured successfully with status: {}", orderId, capturedOrder.status());

            // Update order in database
            Order order = orderRepository.findByPaypalOrderId(orderId)
                    .orElseThrow(() -> new RuntimeException("Order not found: " + orderId));

            order.setStatus(capturedOrder.status());
            orderRepository.save(order);

            // Save payment transaction
            if (capturedOrder.purchaseUnits().size() > 0 &&
                capturedOrder.purchaseUnits().get(0).payments() != null &&
                capturedOrder.purchaseUnits().get(0).payments().captures() != null &&
                !capturedOrder.purchaseUnits().get(0).payments().captures().isEmpty()) {

                Capture capture = capturedOrder.purchaseUnits().get(0).payments().captures().get(0);

                PaymentTransaction transaction = PaymentTransaction.builder()
                        .transactionId(capture.id())
                        .amount(new BigDecimal(capture.amount().value()))
                        .currency(capture.amount().currencyCode())
                        .status(capture.status())
                        .paymentMethod("PAYPAL_VAULT")
                        .order(order)
                        .build();

                paymentTransactionRepository.save(transaction);

                log.info("Vault payment transaction saved: {}", capture.id());
            }

            Map<String, Object> result = new HashMap<>();
            result.put("id", capturedOrder.id());
            result.put("status", capturedOrder.status());
            result.put("purchase_units", capturedOrder.purchaseUnits());
            result.put("payment_method", "vault");
            result.put("payment_method_id", paymentMethodId);

            return result;
        } catch (IOException e) {
            log.error("Error capturing vault order: {}", e.getMessage(), e);
            throw new RuntimeException("Failed to capture vault order: " + e.getMessage(), e);
        }
    }

    @Override
    public Map<String, Object> getOrderDetails(String orderId) {
        // Check if this is a vault order (starts with VAULT_ or DEMO_VAULT_)
        if (orderId.startsWith("VAULT_") || orderId.startsWith("DEMO_VAULT_")) {
            return getVaultOrderDetails(orderId);
        }

        OrdersGetRequest request = new OrdersGetRequest(orderId);

        try {
            HttpResponse<com.paypal.orders.Order> response = payPalHttpClient.execute(request);
            com.paypal.orders.Order paypalOrder = response.result();

            Map<String, Object> result = new HashMap<>();
            result.put("id", paypalOrder.id());
            result.put("status", paypalOrder.status());
            result.put("create_time", paypalOrder.createTime());
            result.put("update_time", paypalOrder.updateTime());

            // 处理 purchase_units，确保包含完整信息
            List<Map<String, Object>> purchaseUnitsData = new ArrayList<>();
            if (paypalOrder.purchaseUnits() != null && !paypalOrder.purchaseUnits().isEmpty()) {
                for (PurchaseUnit unit : paypalOrder.purchaseUnits()) {
                    Map<String, Object> unitData = new HashMap<>();

                    // 添加金额信息
                    if (unit.amountWithBreakdown() != null) {
                        Map<String, Object> amountData = new HashMap<>();
                        amountData.put("currency_code", unit.amountWithBreakdown().currencyCode());
                        amountData.put("value", unit.amountWithBreakdown().value());
                        unitData.put("amount", amountData);
                    }

                    // 添加支付信息（如果存在）
                    if (unit.payments() != null) {
                        Map<String, Object> paymentsData = new HashMap<>();

                        // 添加捕获信息
                        if (unit.payments().captures() != null && !unit.payments().captures().isEmpty()) {
                            List<Map<String, Object>> capturesData = new ArrayList<>();
                            for (Capture capture : unit.payments().captures()) {
                                Map<String, Object> captureData = new HashMap<>();
                                captureData.put("id", capture.id());
                                captureData.put("status", capture.status());

                                if (capture.amount() != null) {
                                    Map<String, Object> captureAmountData = new HashMap<>();
                                    captureAmountData.put("currency_code", capture.amount().currencyCode());
                                    captureAmountData.put("value", capture.amount().value());
                                    captureData.put("amount", captureAmountData);
                                }

                                capturesData.add(captureData);
                            }
                            paymentsData.put("captures", capturesData);
                        }

                        unitData.put("payments", paymentsData);
                    }

                    purchaseUnitsData.add(unitData);
                }
            } else {
                // 如果PayPal返回的purchase_units为空，尝试从数据库获取订单信息
                Order dbOrder = orderRepository.findByPaypalOrderId(orderId).orElse(null);
                if (dbOrder != null) {
                    Map<String, Object> unitData = new HashMap<>();
                    Map<String, Object> amountData = new HashMap<>();
                    amountData.put("currency_code", dbOrder.getCurrency());
                    amountData.put("value", dbOrder.getAmount().toString());
                    unitData.put("amount", amountData);
                    purchaseUnitsData.add(unitData);

                    log.info("Used database order info for missing purchase_units data");
                }
            }

            result.put("purchase_units", purchaseUnitsData);

            // 处理 payer 信息
            Map<String, Object> payerData = new HashMap<>();
            if (paypalOrder.payer() != null) {
                if (paypalOrder.payer().name() != null) {
                    payerData.put("name", paypalOrder.payer().name());
                }
                // 注意：不同版本的PayPal SDK可能有不同的方法名
                try {
                    if (paypalOrder.payer().payerId() != null) {
                        payerData.put("payer_id", paypalOrder.payer().payerId());
                    }
                } catch (Exception e) {
                    log.debug("Could not get payer ID: {}", e.getMessage());
                }
            }
            result.put("payer", payerData);

            log.info("Order details retrieved successfully for order: {}", orderId);
            return result;
        } catch (IOException e) {
            log.error("Error getting PayPal order details for order: {}", orderId, e);
            throw new RuntimeException("Failed to get PayPal order details: " + e.getMessage(), e);
        }
    }

    @Override
    @Transactional
    public Refund processRefund(String captureId, BigDecimal amount, String currency, String reason) {
        // Implementation for refund processing
        // This would use the PayPal Refund API
        // For now, we'll just create a mock refund record

        Order order = orderRepository.findAll().stream()
                .filter(o -> o.getTransactions().stream()
                        .anyMatch(t -> t.getTransactionId().equals(captureId)))
                .findFirst()
                .orElseThrow(() -> new RuntimeException("No order found with capture ID: " + captureId));

        Refund refund = Refund.builder()
                .refundId("REF_" + UUID.randomUUID().toString().substring(0, 8))
                .amount(amount)
                .currency(currency)
                .status("COMPLETED")
                .reason(reason)
                .order(order)
                .build();

        return refundRepository.save(refund);
    }

    @Override
    @Transactional
    public VaultedPaymentMethod vaultPaymentMethod(String customerId, String paymentMethodId,
                                                  String paymentType, String lastFour,
                                                  String brand, boolean isDefault) {
        // If this is set as default, unset any existing default
        if (isDefault) {
            vaultedPaymentMethodRepository.findByCustomerIdAndIsDefaultTrue(customerId)
                    .ifPresent(existingDefault -> {
                        existingDefault.setDefault(false);
                        vaultedPaymentMethodRepository.save(existingDefault);
                    });
        }

        VaultedPaymentMethod paymentMethod = VaultedPaymentMethod.builder()
                .paymentMethodId(paymentMethodId)
                .paymentType(paymentType)
                .customerId(customerId)
                .lastFour(lastFour)
                .brand(brand)
                .isDefault(isDefault)
                .build();

        return vaultedPaymentMethodRepository.save(paymentMethod);
    }

    @Override
    public List<VaultedPaymentMethod> getVaultedPaymentMethods(String customerId) {
        return vaultedPaymentMethodRepository.findByCustomerId(customerId);
    }

    @Override
    public List<Order> getAllOrders() {
        return orderRepository.findAll();
    }

    @Override
    public Order getOrderByPayPalOrderId(String paypalOrderId) {
        return orderRepository.findByPaypalOrderId(paypalOrderId)
                .orElseThrow(() -> new RuntimeException("Order not found: " + paypalOrderId));
    }

    private OrderRequest buildOrderRequest(BigDecimal amount, String currency, String description) {
        OrderRequest orderRequest = new OrderRequest();
        orderRequest.checkoutPaymentIntent("CAPTURE");

        List<PurchaseUnitRequest> purchaseUnits = new ArrayList<>();
        PurchaseUnitRequest purchaseUnit = new PurchaseUnitRequest()
                .description(description)
                .amountWithBreakdown(new AmountWithBreakdown()
                        .currencyCode(currency)
                        .value(amount.toString()));

        purchaseUnits.add(purchaseUnit);
        orderRequest.purchaseUnits(purchaseUnits);

        ApplicationContext applicationContext = new ApplicationContext()
                .brandName("PayPal Demo")
                .landingPage("LOGIN")
                .userAction("PAY_NOW")
                .returnUrl("http://localhost:3000/success")
                .cancelUrl("http://localhost:3000/checkout");

        orderRequest.applicationContext(applicationContext);

        return orderRequest;
    }

    /**
     * Build order request with vault payment source
     */
    private OrderRequest buildVaultOrderRequest(BigDecimal amount, String currency, String description, String paymentMethodId) {
        OrderRequest orderRequest = new OrderRequest();
        orderRequest.checkoutPaymentIntent("CAPTURE");

        // Create amount breakdown
        AmountWithBreakdown amountBreakdown = new AmountWithBreakdown()
                .currencyCode(currency)
                .value(amount.toString());

        // Create purchase unit
        PurchaseUnitRequest purchaseUnitRequest = new PurchaseUnitRequest()
                .amountWithBreakdown(amountBreakdown)
                .description(description);

        orderRequest.purchaseUnits(List.of(purchaseUnitRequest));

        // Add vault payment source
        // Note: In a real implementation, you would use the actual vault token
        // For now, we'll create the order without payment source and rely on immediate capture
        log.info("Building vault order request for payment method: {}", paymentMethodId);

        // Set application context for vault payments
        ApplicationContext applicationContext = new ApplicationContext()
                .returnUrl("http://localhost:3000/success")
                .cancelUrl("http://localhost:3000/checkout")
                .userAction("PAY_NOW"); // Important for vault payments

        orderRequest.applicationContext(applicationContext);

        return orderRequest;
    }

    /**
     * Get vault order details from database
     */
    private Map<String, Object> getVaultOrderDetails(String orderId) {
        try {
            Order order = orderRepository.findByPaypalOrderId(orderId)
                    .orElseThrow(() -> new RuntimeException("Vault order not found: " + orderId));

            Map<String, Object> result = new HashMap<>();
            result.put("id", order.getPaypalOrderId());
            result.put("status", order.getStatus());
            result.put("create_time", order.getCreatedAt().toString());
            result.put("update_time", order.getUpdatedAt().toString());

            // Build purchase units from database data
            Map<String, Object> purchaseUnit = new HashMap<>();
            Map<String, Object> amountData = new HashMap<>();
            amountData.put("value", order.getAmount().toString());
            amountData.put("currency_code", order.getCurrency());
            purchaseUnit.put("amount", amountData);

            // Add payment/capture info if available
            if (!order.getTransactions().isEmpty()) {
                PaymentTransaction transaction = order.getTransactions().get(0);
                Map<String, Object> payments = new HashMap<>();
                Map<String, Object> capture = new HashMap<>();
                capture.put("id", transaction.getTransactionId());
                capture.put("status", transaction.getStatus());
                capture.put("amount", amountData);
                payments.put("captures", List.of(capture));
                purchaseUnit.put("payments", payments);
            }

            result.put("purchase_units", List.of(purchaseUnit));
            result.put("payer", new HashMap<>());

            log.info("Vault order details retrieved successfully for order: {}", orderId);
            return result;
        } catch (Exception e) {
            log.error("Error getting vault order details for order: {}", orderId, e);
            throw new RuntimeException("Failed to get vault order details: " + e.getMessage(), e);
        }
    }
}
