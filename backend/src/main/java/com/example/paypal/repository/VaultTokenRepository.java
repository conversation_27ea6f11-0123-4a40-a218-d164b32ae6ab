package com.example.paypal.repository;

import com.example.paypal.model.VaultToken;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Repository
public interface VaultTokenRepository extends JpaRepository<VaultToken, Long> {
    
    /**
     * Find all active vault tokens for a customer
     */
    List<VaultToken> findByCustomerIdAndIsActiveTrue(String customerId);
    
    /**
     * Find all valid (active and not expired) vault tokens for a customer
     */
    @Query("SELECT vt FROM VaultToken vt WHERE vt.customerId = :customerId " +
           "AND vt.isActive = true AND (vt.expiresAt IS NULL OR vt.expiresAt > :now)")
    List<VaultToken> findValidTokensByCustomerId(@Param("customerId") String customerId, 
                                                @Param("now") LocalDateTime now);
    
    /**
     * Find a specific vault token
     */
    Optional<VaultToken> findByVaultTokenAndIsActiveTrue(String vaultToken);
    
    /**
     * Find default payment method for a customer
     */
    Optional<VaultToken> findByCustomerIdAndIsDefaultTrueAndIsActiveTrue(String customerId);
    
    /**
     * Check if a vault token exists
     */
    boolean existsByVaultToken(String vaultToken);
    
    /**
     * Set all tokens for a customer as non-default
     */
    @Modifying
    @Query("UPDATE VaultToken vt SET vt.isDefault = false, vt.updatedAt = :now " +
           "WHERE vt.customerId = :customerId")
    void clearDefaultForCustomer(@Param("customerId") String customerId, 
                                @Param("now") LocalDateTime now);
    
    /**
     * Find expired tokens
     */
    @Query("SELECT vt FROM VaultToken vt WHERE vt.expiresAt IS NOT NULL AND vt.expiresAt < :now")
    List<VaultToken> findExpiredTokens(@Param("now") LocalDateTime now);
    
    /**
     * Deactivate expired tokens
     */
    @Modifying
    @Query("UPDATE VaultToken vt SET vt.isActive = false, vt.updatedAt = :now " +
           "WHERE vt.expiresAt IS NOT NULL AND vt.expiresAt < :now")
    int deactivateExpiredTokens(@Param("now") LocalDateTime now);
    
    /**
     * Find tokens that haven't been used for a long time
     */
    @Query("SELECT vt FROM VaultToken vt WHERE vt.lastUsedAt IS NOT NULL " +
           "AND vt.lastUsedAt < :cutoffDate AND vt.isActive = true")
    List<VaultToken> findUnusedTokens(@Param("cutoffDate") LocalDateTime cutoffDate);
    
    /**
     * Count active tokens for a customer
     */
    long countByCustomerIdAndIsActiveTrue(String customerId);
}
