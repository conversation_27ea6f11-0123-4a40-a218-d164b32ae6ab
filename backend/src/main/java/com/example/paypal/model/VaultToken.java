package com.example.paypal.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.time.LocalDateTime;

@Entity
@Table(name = "vault_tokens")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class VaultToken {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(name = "customer_id", nullable = false)
    private String customerId;
    
    @Column(name = "vault_token", nullable = false, unique = true)
    private String vaultToken;
    
    @Column(name = "payment_source_type")
    private String paymentSourceType; // CARD, PAYPAL, BANK_ACCOUNT
    
    @Column(name = "card_brand")
    private String cardBrand; // VISA, MASTERCARD, AMEX, etc.
    
    @Column(name = "card_last_four")
    private String cardLastFour;
    
    @Column(name = "card_expiry_month")
    private String cardExpiryMonth;
    
    @Column(name = "card_expiry_year")
    private String cardExpiryYear;
    
    @Column(name = "paypal_email")
    private String paypalEmail; // For PayPal payment source
    
    @Column(name = "is_default")
    private boolean isDefault = false;
    
    @Column(name = "is_active")
    private boolean isActive = true;
    
    @Column(name = "created_at")
    private LocalDateTime createdAt;
    
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;
    
    @Column(name = "expires_at")
    private LocalDateTime expiresAt; // Token expiration time
    
    @Column(name = "last_used_at")
    private LocalDateTime lastUsedAt;
    
    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
        updatedAt = LocalDateTime.now();
        // PayPal vault tokens typically don't expire, but we can set a long expiry
        if (expiresAt == null) {
            expiresAt = LocalDateTime.now().plusYears(5); // 5 years default
        }
    }
    
    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDateTime.now();
    }
    
    /**
     * Check if the token is expired
     */
    public boolean isExpired() {
        return expiresAt != null && LocalDateTime.now().isAfter(expiresAt);
    }
    
    /**
     * Check if the token is valid (active and not expired)
     */
    public boolean isValid() {
        return isActive && !isExpired();
    }
    
    /**
     * Mark token as used
     */
    public void markAsUsed() {
        this.lastUsedAt = LocalDateTime.now();
        this.updatedAt = LocalDateTime.now();
    }
    
    /**
     * Get display name for the payment method
     */
    public String getDisplayName() {
        if ("CARD".equals(paymentSourceType)) {
            return String.format("%s •••• %s", cardBrand, cardLastFour);
        } else if ("PAYPAL".equals(paymentSourceType)) {
            return String.format("PayPal (%s)", paypalEmail);
        }
        return paymentSourceType;
    }
}
