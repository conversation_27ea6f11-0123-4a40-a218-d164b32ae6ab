# Server configuration
server.port=18080

# PayPal configuration
paypal.client.id=AfNDRjiY0upudf0iL2OHXQv3FyMc3St9G6GotrnsqE9xNmGGvLBMYpcrs_cwEqaErT-2WZaQMfTsKGCT
paypal.client.secret=EI93HoxfOFgo0bmVVryOcNW9DwmKy7m22RVenKZ_XmQTXUP4cyARyjqZnDf9YZcU7qNZzcV-oNww1ulO
paypal.mode=sandbox

# Database configuration
spring.datasource.url=jdbc:h2:mem:paypaldb
spring.datasource.driverClassName=org.h2.Driver
spring.datasource.username=sa
spring.datasource.password=password
spring.jpa.database-platform=org.hibernate.dialect.H2Dialect
spring.h2.console.enabled=true
spring.h2.console.path=/h2-console

# JPA configuration
spring.jpa.hibernate.ddl-auto=update
spring.jpa.show-sql=true

# CORS configuration
spring.web.cors.allowed-origins=http://localhost:3000
spring.web.cors.allowed-methods=GET,POST,PUT,DELETE
spring.web.cors.allowed-headers=*
