<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="RemoteRepositoriesConfiguration">
    <remote-repository>
      <option name="id" value="activiti" />
      <option name="name" value="Activiti Repository" />
      <option name="url" value="https://app.camunda.com/nexus/content/groups/public/" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="aliyun" />
      <option name="name" value="Aliyun Repository" />
      <option name="url" value="http://maven.aliyun.com/nexus/content/groups/public/" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="central" />
      <option name="name" value="Maven Central repository" />
      <option name="url" value="https://repo1.maven.org/maven2" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="springsource" />
      <option name="name" value="SpringSource Repository" />
      <option name="url" value="http://repo.spring.io/release/" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="jboss.community" />
      <option name="name" value="JBoss Community repository" />
      <option name="url" value="https://repository.jboss.org/nexus/content/repositories/public/" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="central" />
      <option name="name" value="Central Repository" />
      <option name="url" value="https://maven.aliyun.com/repository/public" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="aliyun-google" />
      <option name="name" value="Aliyun Google Repository" />
      <option name="url" value="https://maven.aliyun.com/nexus/content/repositories/google/" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="thinkgem" />
      <option name="name" value="ThinkGem Repository" />
      <option name="url" value="https://raw.github.com/thinkgem/repository/master/" />
    </remote-repository>
  </component>
</project>